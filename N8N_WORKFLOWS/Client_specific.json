{"name": "Client specific", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "trigger_type"}, {"name": "project_data", "type": "json"}, {"name": "customer_info", "type": "json"}, {"name": "step_override"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-260, -100], "id": "6eb19fb7-b68f-4a23-97ac-5f9031371467", "name": "🏗️ <PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"promptType": "define", "text": "={{ JSON.stringify($json) }}", "options": {"systemMessage": "=Du bist der Bauprozess-<PERSON> <PERSON> für die Huly-basierte Projektsteuerung.\n\n**Deine Aufgaben:**\n1. **Projektmanagement**: Bauvorhaben in <PERSON><PERSON> von Anfrage bis Abrechnung verwalten\n2. **Huly Integration**: Tasks, Issues, Documents in Huly automatisch erstellen/aktualisieren\n3. **Terminkoordination**: Begehungen, Bautermine, Abnahmen über Huly planen\n4. **Dokumentenverwaltung**: Angebote, Pläne, LZA-Meldungen in Huly verwalten\n5. **HITL Orchestration**: Freigabe-Workflows über Huly + Telegram\n\n**Verfügbare Tools:**\n- huly_project_manager: <PERSON>je<PERSON><PERSON> in Huly erstellen/verwalten\n- huly_task_creator: Tasks für Prozessschritte erstellen\n- huly_document_manager: Dokumente in Huly hochladen/verwalten\n- calendar_coordinator: <PERSON><PERSON><PERSON> für Begehungen/Baumaßnahmen\n- email_automation: Kundenkorrespondenz\n- telegram_hitl: HITL-Freigaben anfordern\n- progress_tracker: <PERSON><PERSON><PERSON><PERSON>ritt in Huly aktualisieren\n\n**14 Bauprozess-Schritte (als Huly Tasks):**\n1. Eingang Anfrage → Huly Issue erstellen\n2. Terminvorschläge Begehung → Huly Task + Kalender\n3. Angebot Planung → Huly Document + HITL Preisfreigabe\n4. Auftrag Planungsleistungen → Huly Task\n5. Entwurfsplanung → Huly Document + HITL Qualitätskontrolle\n6. Freigabe Planung → Huly Status Update\n7. Ausführungsplanung + Angebot → Huly Document + HITL Preisfreigabe\n8. Beauftragung AG → Huly Milestone + HITL Kapazitätsprüfung\n9. Bautermin übermitteln → Huly Task + Kalender\n10. LZA Fertigmeldung → Huly Document\n11. Terminvorschläge Abnahme → Huly Task + Kalender\n12. Abnahme + Aufmaß → Huly Task\n13. Luftmaß → Huly Document\n14. Freigabe Rechnung → HITL Aufmaß-Kontrolle + Huly Completion\n\n**Huly Workspace Structure:**\n- Space: 'Bauprojekte'\n- Project Templates: 'Neubau', 'Sanierung', 'Umbau'\n- Task Categories: 'Planung', 'Ausführung', 'Abnahme', 'Abrechnung'\n- Document Types: 'Angebot', 'Plan', 'LZA', 'Rechnung'\n\n**HITL Integration:** Telegram für sofortige Freigaben + Huly für Dokumentation"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [140, -100], "id": "6d89a21e-78d1-4e8c-8166-f1965181edea", "name": "🧠 Bauprozess AI Brain"}, {"parameters": {"method": "POST", "url": "{{ $env.HULY_API_URL }}/api/projects", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"workspace\": \"{{ $env.HULY_WORKSPACE_ID }}\",\n  \"space\": \"Bauprojekte\",\n  \"project\": {\n    \"title\": \"{{ $fromAI('project_title', 'Project title') }}\",\n    \"description\": \"{{ $fromAI('project_description', 'Project description') }}\",\n    \"template\": \"{{ $fromAI('project_template', 'Project template: Neubau, Sanierung, Umbau') }}\",\n    \"status\": \"{{ $fromAI('project_status', 'Project status') }}\",\n    \"priority\": \"{{ $fromAI('priority', 'Priority: low, normal, high, urgent') }}\",\n    \"assignee\": \"{{ $fromAI('assignee', 'Responsible person') }}\",\n    \"dueDate\": \"{{ $fromAI('due_date', 'Project due date') }}\",\n    \"customer\": {\n      \"name\": \"{{ $fromAI('customer_name', 'Customer name') }}\",\n      \"email\": \"{{ $fromAI('customer_email', 'Customer email') }}\",\n      \"phone\": \"{{ $fromAI('customer_phone', 'Customer phone') }}\",\n      \"address\": \"{{ $fromAI('customer_address', 'Customer address') }}\"\n    },\n    \"budget\": \"{{ $fromAI('project_budget', 'Project budget in EUR') }}\",\n    \"tags\": [\"{{ $fromAI('project_type', 'Construction type') }}\", \"bauprojekt\"],\n    \"customFields\": {\n      \"projectId\": \"{{ $fromAI('project_id', 'Unique project identifier') }}\",\n      \"constructionType\": \"{{ $fromAI('construction_type', 'Type of construction') }}\",\n      \"location\": \"{{ $fromAI('location', 'Construction location') }}\",\n      \"plannerAssigned\": \"{{ $fromAI('planner', 'Assigned planner') }}\",\n      \"constructionManager\": \"{{ $fromAI('construction_manager', 'Construction manager') }}\"\n    }\n  }\n}"}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [740, 140], "id": "79d51fc2-518e-4f2c-86ef-96fab4e48ac8", "name": "🏢 <PERSON><PERSON> Project Manager"}, {"parameters": {"method": "POST", "url": "{{ $env.HULY_API_URL }}/api/tasks", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"workspace\": \"{{ $env.HULY_WORKSPACE_ID }}\",\n  \"project\": \"{{ $fromAI('huly_project_id', 'Huly project ID') }}\",\n  \"task\": {\n    \"title\": \"{{ $fromAI('task_title', 'Task title') }}\",\n    \"description\": \"{{ $fromAI('task_description', 'Detailed task description') }}\",\n    \"category\": \"{{ $fromAI('task_category', 'Task category: Planung, Ausführung, Abnahme, Abrechnung') }}\",\n    \"status\": \"{{ $fromAI('task_status', 'Task status: todo, in-progress, review, done') }}\",\n    \"priority\": \"{{ $fromAI('task_priority', 'Task priority: low, normal, high, urgent') }}\",\n    \"assignee\": \"{{ $fromAI('task_assignee', 'Person assigned to task') }}\",\n    \"dueDate\": \"{{ $fromAI('task_due_date', 'Task due date') }}\",\n    \"estimatedHours\": \"{{ $fromAI('estimated_hours', 'Estimated hours for completion') }}\",\n    \"labels\": [\"{{ $fromAI('process_step', 'Process step number') }}\", \"bauprozess\"],\n    \"dependencies\": \"{{ $fromAI('dependencies', 'Task dependencies as array') }}\",\n    \"checklist\": \"{{ $fromAI('checklist', 'Task checklist items as array') }}\",\n    \"customFields\": {\n      \"processStep\": \"{{ $fromAI('step_number', 'Process step number 1-14') }}\",\n      \"requiresHITL\": \"{{ $fromAI('requires_hitl', 'Does this step require human approval?') }}\",\n      \"automationPossible\": \"{{ $fromAI('automation_possible', 'Can this step be automated?') }}\",\n      \"documentsRequired\": \"{{ $fromAI('documents_required', 'Required documents as array') }}\"\n    }\n  }\n}"}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [900, 240], "id": "60f15643-3c4a-4f65-b366-79f5d07b90b9", "name": "📋 Huly Task Creator"}, {"parameters": {"method": "POST", "url": "{{ $env.HULY_API_URL }}/api/documents", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"workspace\": \"{{ $env.HULY_WORKSPACE_ID }}\",\n  \"project\": \"{{ $fromAI('huly_project_id', 'Huly project ID') }}\",\n  \"document\": {\n    \"title\": \"{{ $fromAI('document_title', 'Document title') }}\",\n    \"type\": \"{{ $fromAI('document_type', 'Document type: Angebot, Plan, LZA, Rechnung, Vertrag') }}\",\n    \"description\": \"{{ $fromAI('document_description', 'Document description') }}\",\n    \"fileUrl\": \"{{ $fromAI('file_url', 'URL to document file') }}\",\n    \"fileName\": \"{{ $fromAI('file_name', 'Document file name') }}\",\n    \"mimeType\": \"{{ $fromAI('mime_type', 'Document MIME type') }}\",\n    \"version\": \"{{ $fromAI('version', 'Document version number') }}\",\n    \"status\": \"{{ $fromAI('document_status', 'Document status: draft, review, approved, sent') }}\",\n    \"assignedTo\": \"{{ $fromAI('document_owner', 'Document owner/reviewer') }}\",\n    \"tags\": [\"{{ $fromAI('process_step', 'Related process step') }}\", \"{{ $fromAI('document_category', 'Document category') }}\"],\n    \"metadata\": {\n      \"processStep\": \"{{ $fromAI('step_number', 'Related process step number') }}\",\n      \"customerVisible\": \"{{ $fromAI('customer_visible', 'Should customer see this document?') }}\",\n      \"requiresApproval\": \"{{ $fromAI('requires_approval', 'Does document need approval?') }}\",\n      \"sentToCustomer\": \"{{ $fromAI('sent_to_customer', 'Was document sent to customer?') }}\",\n      \"customerConfirmed\": \"{{ $fromAI('customer_confirmed', 'Did customer confirm receipt?') }}\"\n    }\n  }\n}"}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [960, 120], "id": "cda5abaa-6ac1-461e-98e8-4293a885b973", "name": "📄 Huly Document Manager"}, {"parameters": {"operation": "send"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1140, 100], "id": "88ad5f04-f6e6-4256-aa82-1310fda96f38", "name": "📱 Telegram HITL", "webhookId": "384a186e-a4af-494e-848d-5845f7c614bd"}, {"parameters": {"operation": "upsert"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [540, 300], "id": "1f96e963-9074-41ca-8166-f8a259d0db9d", "name": "📊 Progress Tracker"}, {"parameters": {"calendar": {"__rl": true, "value": "primary", "mode": "list"}, "start": "={{ $fromAI('appointment_start', 'Appointment start date and time') }}", "end": "={{ $fromAI('appointment_end', 'Appointment end date and time') }}", "additionalFields": {"attendees": "={{ $fromAI('attendees', 'Attendee emails comma-separated') }}", "description": "={{ $fromAI('appointment_description', 'Appointment description with Huly links') }}", "location": "={{ $fromAI('appointment_location', 'Appointment location') }}", "summary": "={{ $fromAI('appointment_title', 'Appointment title') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [740, 300], "id": "3e00bc25-2875-4bd3-9220-250a0da6b46e", "name": "📅 <PERSON><PERSON><PERSON>"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "construction-{{ $json.project_id }}", "contextWindowLength": 50}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [140, 100], "id": "2a46f850-72d8-48d2-b9ef-fe2644452968", "name": "🧠 Project Memory"}, {"parameters": {"model": "gpt-4-turbo-preview", "options": {"maxTokens": 3000, "temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [140, 200], "id": "55a25bfd-f4c8-43fd-a02e-473ad1e05442", "name": "🤖 Construction LLM"}, {"parameters": {"functionCode": "// Format Huly-integrated construction process results\nconst result = items[0].json;\nconst trigger = $('🏗️ Bauprozess Trigger').first().json;\n\n// Parse AI output\nlet processUpdate;\ntry {\n  processUpdate = typeof result.output === 'string' ? JSON.parse(result.output) : result.output;\n} catch (e) {\n  processUpdate = {\n    action: 'process_step',\n    step: 'unknown',\n    status: 'pending',\n    huly_integration: 'pending'\n  };\n}\n\n// Map to Huly process steps\nconst hulyStepMapping = {\n  1: { name: 'Eingang Anfrage', category: 'Planung', requiresHITL: false },\n  2: { name: 'Terminvorschläge Begehung', category: 'Planung', requiresHITL: false },\n  3: { name: 'Angebot für Planung', category: 'Planung', requiresHITL: true, reason: 'Preisfreigabe erforderlich' },\n  4: { name: 'Auftrag Planungsleistungen', category: 'Planung', requiresHITL: false },\n  5: { name: 'Entwurfsplanung', category: 'Planung', requiresHITL: true, reason: 'Qualitätskontrolle erforderlich' },\n  6: { name: 'Freigabe der Planung', category: 'Planung', requiresHITL: false },\n  7: { name: 'Ausführungsplanung + Angebot', category: 'Ausführung', requiresHITL: true, reason: 'Preisfreigabe erforderlich' },\n  8: { name: 'Beauftragung vom AG', category: 'Ausführung', requiresHITL: true, reason: 'Kapazitätsprüfung erforderlich' },\n  9: { name: 'Bautermin übermitteln', category: 'Ausführung', requiresHITL: false },\n  10: { name: 'LZA Fertigmeldung', category: 'Ausführung', requiresHITL: false },\n  11: { name: 'Terminvorschläge Abnahme', category: 'Abnahme', requiresHITL: false },\n  12: { name: 'Abnahme und Aufmaß', category: 'Abnahme', requiresHITL: false },\n  13: { name: 'Luftmaß versendet', category: 'Abrechnung', requiresHITL: false },\n  14: { name: 'Freigabe zur Rechnung', category: 'Abrechnung', requiresHITL: true, reason: 'Aufmaß-Kontrolle erforderlich' }\n};\n\nconst currentStepNum = parseInt(processUpdate.current_step) || 1;\nconst currentStepInfo = hulyStepMapping[currentStepNum] || hulyStepMapping[1];\n\n// Calculate progress percentage\nconst progressPercentage = Math.round((currentStepNum / 14) * 100);\n\n// Determine next actions based on current step\nconst nextActions = {\n  1: ['Projekt in Huly anlegen', 'Planungsabteilung zuweisen'],\n  2: ['Begehungstermin vorschlagen', 'Kalendertermin erstellen'],\n  3: ['Angebot erstellen', 'Preisfreigabe anfordern'],\n  4: ['Planungsauftrag in Huly dokumentieren'],\n  5: ['Entwurfsplanung hochladen', 'Qualitätsprüfung anfordern'],\n  6: ['Planungsfreigabe dokumentieren', 'Ausführungsplanung starten'],\n  7: ['Ausführungsplanung + Angebot erstellen', 'Preisfreigabe anfordern'],\n  8: ['Beauftragung dokumentieren', 'Kapazitätsprüfung durchführen'],\n  9: ['Bautermin kommunizieren', 'Bauleiter zuweisen'],\n  10: ['LZA-Meldung versenden', 'Fortschritt dokumentieren'],\n  11: ['Abnahmetermin koordinieren'],\n  12: ['Abnahme durchführen', 'Aufmaß erstellen'],\n  13: ['Luftmaß versenden'],\n  14: ['Aufmaß prüfen', 'Rechnungsfreigabe']\n};\n\nreturn [{\n  json: {\n    status: 'success',\n    agent: 'huly_construction_agent',\n    project_id: processUpdate.project_id || trigger.project_data?.project_id,\n    huly_project_id: processUpdate.huly_project_id,\n    current_step: currentStepNum,\n    step_name: currentStepInfo.name,\n    step_category: currentStepInfo.category,\n    progress_percentage: progressPercentage,\n    requires_hitl: currentStepInfo.requiresHITL || false,\n    hitl_reason: currentStepInfo.reason || null,\n    next_actions: nextActions[currentStepNum] || ['Nächster Schritt definieren'],\n    huly_integration: {\n      project_created: processUpdate.huly_project_created || false,\n      task_created: processUpdate.huly_task_created || false,\n      documents_uploaded: processUpdate.documents_uploaded || 0,\n      team_notified: processUpdate.team_notified || false\n    },\n    timeline: {\n      started: processUpdate.started || new Date().toISOString(),\n      estimated_completion: processUpdate.estimated_completion,\n      last_updated: new Date().toISOString(),\n      next_milestone: hulyStepMapping[currentStepNum + 1]?.name || 'Projekt abgeschlossen'\n    },\n    notifications: {\n      customer: processUpdate.notify_customer || false,\n      team: processUpdate.notify_team || true,\n      huly_updated: true,\n      telegram_sent: currentStepInfo.requiresHITL\n    }\n  }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [540, -100], "id": "a36f399c-14c9-4c8c-a655-a5e39b3488d5", "name": "📋 Results Formatter"}, {"parameters": {"conditions": {"conditions": [{"leftValue": "={{ $json.requires_hitl }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}]}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [740, -100], "id": "26b28500-61e3-464d-b008-a97b98e557f4", "name": "❓ HITL Required?"}, {"parameters": {"operation": "append"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1140, -100], "id": "eaf5825e-977b-4d55-b9ca-73d8cfe138f9", "name": "📊 Activity Logger"}], "pinData": {}, "connections": {"🏗️ Bauprozess Trigger": {"main": [[{"node": "🧠 Bauprozess AI Brain", "type": "main", "index": 0}]]}, "🧠 Bauprozess AI Brain": {"main": [[{"node": "📋 Results Formatter", "type": "main", "index": 0}]]}, "📋 Results Formatter": {"main": [[{"node": "❓ HITL Required?", "type": "main", "index": 0}, {"node": "📊 Activity Logger", "type": "main", "index": 0}]]}, "❓ HITL Required?": {"main": [[{"node": "📱 Telegram HITL", "type": "main", "index": 0}]]}, "🤖 Construction LLM": {"ai_languageModel": [[{"node": "🧠 Bauprozess AI Brain", "type": "ai_languageModel", "index": 0}]]}, "🧠 Project Memory": {"ai_memory": [[{"node": "🧠 Bauprozess AI Brain", "type": "ai_memory", "index": 0}]]}, "🏢 Huly Project Manager": {"ai_tool": [[{"node": "🧠 Bauprozess AI Brain", "type": "ai_tool", "index": 0}]]}, "📋 Huly Task Creator": {"ai_tool": [[{"node": "🧠 Bauprozess AI Brain", "type": "ai_tool", "index": 0}]]}, "📄 Huly Document Manager": {"ai_tool": [[{"node": "🧠 Bauprozess AI Brain", "type": "ai_tool", "index": 0}]]}, "📅 Kalender Koordinator": {"ai_tool": [[{"node": "🧠 Bauprozess AI Brain", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "d51c9293-ca37-429e-85f4-ea35ae00eb48", "meta": {"templateCredsSetupCompleted": true, "instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "271EPkVjJ5f8k0Cx", "tags": []}
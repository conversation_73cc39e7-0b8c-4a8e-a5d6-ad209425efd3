{"name": "Enhanced LLM rotation manager", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "prompt"}, {"name": "systemMessage"}, {"name": "temperature", "type": "number"}, {"name": "maxTokens", "type": "number"}, {"name": "preferredModel"}, {"name": "requiresVision", "type": "boolean"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-800, 220], "id": "1a6344d8-4423-47ae-af88-8bfe99e099c8", "name": "Enhanced Workflow Input"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "=-- Hole aktuelle API-Statistiken mit erweiterten Metriken\nSELECT \n  provider_name,\n  model_name,\n  daily_limit,\n  monthly_limit,\n  daily_used,\n  monthly_used,\n  avg_response_time_ms,\n  error_rate_24h,\n  cost_per_1k_tokens,\n  supports_vision,\n  max_tokens,\n  last_success,\n  is_active\nFROM llm_provider_stats \nWHERE is_active = true\nORDER BY \n  (daily_limit - daily_used) DESC,\n  cost_per_1k_tokens ASC,\n  avg_response_time_ms ASC", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-600, 220], "id": "1638b469-1790-46d0-9715-8fce189d23ed", "name": "Get Provider Statistics"}, {"parameters": {"functionCode": "// Enhanced Provider Selection with multiple criteria\nconst providers = items[0].json || [];\nconst request = $('Enhanced Workflow Input').first().json;\n\n// Provider capability scoring\nconst scoredProviders = providers.map(provider => {\n  let score = 0;\n  \n  // Availability Score (40%)\n  const dailyAvailable = (provider.daily_limit - provider.daily_used) / provider.daily_limit;\n  const monthlyAvailable = (provider.monthly_limit - provider.monthly_used) / provider.monthly_limit;\n  score += (dailyAvailable * 0.2 + monthlyAvailable * 0.2) * 100;\n  \n  // Performance Score (25%)\n  const maxResponseTime = 5000; // 5 seconds\n  const responseScore = Math.max(0, (maxResponseTime - provider.avg_response_time_ms) / maxResponseTime);\n  const errorScore = Math.max(0, (1 - provider.error_rate_24h));\n  score += (responseScore * 0.15 + errorScore * 0.1) * 100;\n  \n  // Cost Efficiency (20%)\n  const avgCost = 0.01; // Average cost benchmark\n  const costScore = Math.min(2, avgCost / provider.cost_per_1k_tokens);\n  score += costScore * 20;\n  \n  // Capability Match (15%)\n  if (request.requiresVision && !provider.supports_vision) {\n    score -= 50; // Heavy penalty for missing required capability\n  }\n  if (request.maxTokens > provider.max_tokens) {\n    score -= 30; // Penalty for insufficient token limit\n  }\n  if (request.preferredModel !== 'auto' && provider.model_name === request.preferredModel) {\n    score += 25; // Bonus for preferred model\n  }\n  \n  return {\n    ...provider,\n    score,\n    canHandle: dailyAvailable > 0 && monthlyAvailable > 0 && \n               (!request.requiresVision || provider.supports_vision) &&\n               request.maxTokens <= provider.max_tokens\n  };\n});\n\n// Select best available provider\nconst availableProviders = scoredProviders.filter(p => p.canHandle);\n\nif (availableProviders.length === 0) {\n  throw new Error('Keine geeigneten LLM-Provider verfügbar! Alle Limits erreicht oder Anforderungen nicht erfüllbar.');\n}\n\nconst selectedProvider = availableProviders.sort((a, b) => b.score - a.score)[0];\n\n// Enhanced logging\nconsole.log(`Selected Provider: ${selectedProvider.provider_name} (${selectedProvider.model_name})`);\nconsole.log(`Score: ${selectedProvider.score.toFixed(2)}`);\nconsole.log(`Daily remaining: ${selectedProvider.daily_limit - selectedProvider.daily_used}`);\n\nreturn [{\n  json: {\n    selectedProvider,\n    alternatives: availableProviders.slice(1, 3),\n    request,\n    selectionReason: `Gewählt wegen Score ${selectedProvider.score.toFixed(2)} - Verfügbarkeit: ${((selectedProvider.daily_limit - selectedProvider.daily_used) / selectedProvider.daily_limit * 100).toFixed(1)}%`,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-400, 220], "id": "98568bc0-878e-4dee-a2db-e5f20327e237", "name": "Enhanced Provider Selection"}, {"parameters": {"rules": {"values": [{"conditions": {"conditions": [{"leftValue": "={{ $json.selectedProvider.provider_name }}", "rightValue": "openai", "operator": {"type": "string", "operation": "equals"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.selectedProvider.provider_name }}", "rightValue": "anthropic", "operator": {"type": "string", "operation": "equals"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.selectedProvider.provider_name }}", "rightValue": "google", "operator": {"type": "string", "operation": "equals"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.selectedProvider.provider_name }}", "rightValue": "groq", "operator": {"type": "string", "operation": "equals"}}]}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-200, 220], "id": "d7c2e4a0-6ec4-49ec-9bf6-fa883566052e", "name": "Enhanced Provider Router"}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": ""}, "messages": {"values": [{"content": "={{ $json.request.systemMessage }}", "role": "system"}, {"content": "={{ $json.request.prompt }}"}]}, "options": {"maxTokens": "={{ $json.request.maxTokens }}", "temperature": "={{ $json.request.temperature }}"}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [0, 20], "id": "f4267ef6-13f0-46bd-9cbd-1f1de330c8fd", "name": "OpenAI"}, {"parameters": {"model": "={{ $json.selectedProvider.model_name }}", "options": {"temperature": "={{ $json.request.temperature }}"}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.2, "position": [0, 180], "id": "13bc32c9-a617-4629-a7c8-2f461c780bdb", "name": "Anthropic"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.lmChatGooglePalm", "typeVersion": 1, "position": [0, 340], "id": "e3139a14-4580-4083-ab56-ca4044ac3f95", "name": "Google Gemini"}, {"parameters": {"method": "POST", "url": "https://api.groq.com/openai/v1/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"{{ $json.selectedProvider.model_name }}\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"{{ $json.request.systemMessage }}\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{ $json.request.prompt }}\"\n    }\n  ],\n  \"temperature\": {{ $json.request.temperature }},\n  \"max_tokens\": {{ $json.request.maxTokens }}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 500], "id": "402223ec-f85e-4719-be24-53155d086c23", "name": "Groq"}, {"parameters": {"mode": "combine", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [200, 220], "id": "7fd687cf-541e-4c98-9bae-84642f44f222", "name": "Merge Responses"}, {"parameters": {"functionCode": "// Enhanced response formatting with metrics\nconst response = items[0].json;\nconst selection = $('Enhanced Provider Selection').first().json;\nconst startTime = new Date(selection.timestamp);\nconst endTime = new Date();\nconst responseTime = endTime - startTime;\n\n// Extract content based on provider\nlet content, usage = {};\nconst provider = selection.selectedProvider.provider_name;\n\nswitch(provider) {\n  case 'openai':\n    content = response.message?.content || response.choices?.[0]?.message?.content || '';\n    usage = response.usage || {};\n    break;\n  case 'anthropic':\n    content = response.content?.[0]?.text || response.completion || '';\n    usage = response.usage || {};\n    break;\n  case 'google':\n    content = response.text || response.candidates?.[0]?.content || '';\n    usage = {\n      prompt_tokens: response.usage?.promptTokenCount || 0,\n      completion_tokens: response.usage?.candidatesTokenCount || 0\n    };\n    break;\n  case 'groq':\n    content = response.choices?.[0]?.message?.content || '';\n    usage = response.usage || {};\n    break;\n}\n\n// Calculate total tokens if missing\nif (!usage.total_tokens) {\n  usage.total_tokens = (usage.prompt_tokens || 0) + (usage.completion_tokens || 0);\n}\n\n// Calculate cost\nconst cost = (usage.total_tokens / 1000) * selection.selectedProvider.cost_per_1k_tokens;\n\nreturn [{\n  json: {\n    content,\n    metadata: {\n      provider: selection.selectedProvider.provider_name,\n      model: selection.selectedProvider.model_name,\n      responseTime,\n      usage,\n      cost,\n      selectionReason: selection.selectionReason,\n      alternatives: selection.alternatives.map(a => ({\n        provider: a.provider_name,\n        model: a.model_name,\n        score: a.score.toFixed(2)\n      })),\n      timestamp: endTime.toISOString()\n    },\n    success: true\n  }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [400, 220], "id": "60dec277-b79f-467c-902c-198731ed24a9", "name": "Format Enhanced Response"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "=-- Update provider statistics\nINSERT INTO llm_usage_logs (\n  provider_name,\n  model_name,\n  prompt_tokens,\n  completion_tokens,\n  total_tokens,\n  response_time_ms,\n  cost,\n  success,\n  timestamp\n) VALUES (\n  '{{ $json.metadata.provider }}',\n  '{{ $json.metadata.model }}',\n  {{ $json.metadata.usage.prompt_tokens || 0 }},\n  {{ $json.metadata.usage.completion_tokens || 0 }},\n  {{ $json.metadata.usage.total_tokens || 0 }},\n  {{ $json.metadata.responseTime }},\n  {{ $json.metadata.cost }},\n  true,\n  NOW()\n);\n\n-- Update daily usage\nUPDATE llm_provider_stats \nSET \n  daily_used = daily_used + {{ $json.metadata.usage.total_tokens || 0 }},\n  monthly_used = monthly_used + {{ $json.metadata.usage.total_tokens || 0 }},\n  last_success = NOW(),\n  total_requests = total_requests + 1,\n  avg_response_time_ms = (avg_response_time_ms * (total_requests - 1) + {{ $json.metadata.responseTime }}) / total_requests\nWHERE provider_name = '{{ $json.metadata.provider }}' \n  AND model_name = '{{ $json.metadata.model }}';", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [600, 220], "id": "d64e6759-792b-430c-9c6d-d0a258673177", "name": "Update Statistics"}, {"parameters": {"conditions": {"conditions": [{"leftValue": "={{ $json.metadata.cost }}", "rightValue": 0.1, "operator": {"type": "number", "operation": "larger"}}]}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [400, 420], "id": "443c1d3a-8e2e-411b-b284-38dc74269fa8", "name": "High Cost Alert?"}, {"parameters": {"chatId": "{{ $env.TELEGRAM_ADMIN_CHAT }}", "text": "=💰 **Hohe LLM-Kosten Warning**\n\n**Provider:** {{ $json.metadata.provider }}\n**Model:** {{ $json.metadata.model }}\n**<PERSON>sten:** ${{ $json.metadata.cost.toFixed(4) }}\n**Tokens:** {{ $json.metadata.usage.total_tokens }}\n**Response Zeit:** {{ $json.metadata.responseTime }}ms\n\n**Grund:** {{ $json.metadata.selectionReason }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [600, 520], "id": "65f45676-8af0-416f-b0df-042e76fc97e7", "name": "Cost <PERSON><PERSON>", "webhookId": "9b95c59d-6997-4f12-bfa4-cbb659f88c62"}, {"parameters": {"content": "## Provider Database SCheme\n-- LLM Provider Statistics Table\nCREATE TABLE llm_provider_stats (\n    id SERIAL PRIMARY KEY,\n    provider_name VARCHAR(50) NOT NULL,\n    model_name VARCHAR(100) NOT NULL,\n    daily_limit INTEGER NOT NULL,\n    monthly_limit INTEGER NOT NULL,\n    daily_used INTEGER DEFAULT 0,\n    monthly_used INTEGER DEFAULT 0,\n    avg_response_time_ms INTEGER DEFAULT 1000,\n    error_rate_24h DECIMAL(5,4) DEFAULT 0.0000,\n    cost_per_1k_tokens DECIMAL(8,6) NOT NULL,\n    supports_vision BOOLEAN DEFAULT false,\n    max_tokens INTEGER DEFAULT 4096,\n    last_success TIMESTAMP,\n    last_reset_daily DATE DEFAULT CURRENT_DATE,\n    last_reset_monthly DATE DEFAULT DATE_TRUNC('month', CURRENT_DATE),\n    total_requests INTEGER DEFAULT 0,\n    total_tokens BIGINT DEFAULT 0,\n    total_cost DECIMAL(10,4) DEFAULT 0,\n    is_active BOOLEAN DEFAULT true,\n    created_at TIMESTAMP DEFAULT NOW(),\n    updated_at TIMESTAMP DEFAULT NOW(),\n    UNIQUE(provider_name, model_name)\n);\n\n-- LLM Usage Logs Table\nCREATE TABLE llm_usage_logs (\n    id SERIAL PRIMARY KEY,\n    provider_name VARCHAR(50) NOT NULL,\n    model_name VARCHAR(100) NOT NULL,\n    prompt_tokens INTEGER DEFAULT 0,\n    completion_tokens INTEGER DEFAULT 0,\n    total_tokens INTEGER DEFAULT 0,\n    response_time_ms INTEGER,\n    cost DECIMAL(8,6) DEFAULT 0,\n    success BOOLEAN DEFAULT true,\n    error_message TEXT,\n    timestamp TIMESTAMP DEFAULT NOW(),\n    user_id VARCHAR(100),\n    request_type VARCHAR(50), -- 'chat', 'completion', 'embedding'\n    metadata JSONB\n);\n\n-- Insert default providers\nINSERT INTO llm_provider_stats (\n    provider_name, model_name, daily_limit, monthly_limit, \n    cost_per_1k_tokens, supports_vision, max_tokens\n) VALUES\n-- OpenAI Models\n('openai', 'gpt-4o', 100, 3000, 0.005, true, 128000),\n('openai', 'gpt-4o-mini', 500, 15000, 0.00015, true, 128000),\n('openai', 'gpt-4-turbo', 200, 6000, 0.003, true, 128000),\n('openai', 'gpt-3.5-turbo', 1000, 30000, 0.0005, false, 16385),\n\n-- Anthropic Models\n('anthropic', 'claude-3-5-sonnet-20241022', 200, 6000, 0.003, true, 200000),\n('anthropic', 'claude-3-5-haiku-20241022', 500, 15000, 0.00025, false, 200000),\n('anthropic', 'claude-3-opus-20240229', 50, 1500, 0.015, true, 200000),\n\n-- Google Models\n('google', 'gemini-2.0-flash-exp', 1000, 30000, 0.000075, true, 1000000),\n('google', 'gemini-1.5-pro', 300, 9000, 0.00125, true, 2000000),\n('google', 'gemini-1.5-flash', 1500, 45000, 0.000075, true, 1000000),\n\n-- Groq Models\n('groq', 'llama-3.2-90b-text-preview', 200, 6000, 0.00059, false, 8192),\n('groq', 'llama-3.1-70b-versatile', 300, 9000, 0.00059, false, 32768),\n('groq', 'mixtral-8x7b-32768', 500, 15000, 0.00024, false, 32768);\n\n-- Auto-reset daily limits\nCREATE OR REPLACE FUNCTION reset_daily_limits()\nRETURNS VOID AS $$\nBEGIN\n    UPDATE llm_provider_stats \n    SET daily_used = 0, last_reset_daily = CURRENT_DATE\n    WHERE last_reset_daily < CURRENT_DATE;\nEND;\n$$ LANGUAGE plpgsql;\n\n-- Auto-reset monthly limits  \nCREATE OR REPLACE FUNCTION reset_monthly_limits()\nRETURNS VOID AS $$\nBEGIN\n    UPDATE llm_provider_stats \n    SET monthly_used = 0, last_reset_monthly = DATE_TRUNC('month', CURRENT_DATE)\n    WHERE last_reset_monthly < DATE_TRUNC('month', CURRENT_DATE);\nEND;\n$$ LANGUAGE plpgsql;\n\n-- Create indexes for performance\nCREATE INDEX idx_llm_stats_provider_model ON llm_provider_stats(provider_name, model_name);\nCREATE INDEX idx_llm_stats_active ON llm_provider_stats(is_active);\nCREATE INDEX idx_llm_usage_timestamp ON llm_usage_logs(timestamp);\nCREATE INDEX idx_llm_usage_provider ON llm_usage_logs(provider_name, model_name);\n\n-- View for provider dashboard\nCREATE VIEW llm_provider_dashboard AS\nSELECT \n    provider_name,\n    model_name,\n    daily_limit,\n    daily_used,\n    ROUND((daily_used::float / daily_limit * 100), 2) as daily_usage_percent,\n    monthly_limit,\n    monthly_used,\n    ROUND((monthly_used::float / monthly_limit * 100), 2) as monthly_usage_percent,\n    avg_response_time_ms,\n    error_rate_24h,\n    cost_per_1k_tokens,\n    total_requests,\n    ROUND(total_cost::numeric, 4) as total_cost,\n    is_active,\n    last_success\nFROM llm_provider_stats\nORDER BY \n    is_active DESC,\n    daily_usage_percent ASC,\n    cost_per_1k_tokens ASC;"}, "type": "n8n-nodes-base.stickyNote", "position": [-840, -100], "typeVersion": 1, "id": "919a5202-5518-4f39-9f82-ce0556a00020", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"Enhanced Workflow Input": {"main": [[{"node": "Get Provider Statistics", "type": "main", "index": 0}]]}, "Get Provider Statistics": {"main": [[{"node": "Enhanced Provider Selection", "type": "main", "index": 0}]]}, "Enhanced Provider Selection": {"main": [[{"node": "Enhanced Provider Router", "type": "main", "index": 0}]]}, "Enhanced Provider Router": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}], [], [], [{"node": "Groq", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Merge Responses", "type": "main", "index": 0}]]}, "Groq": {"main": [[{"node": "Merge Responses", "type": "main", "index": 0}]]}, "Merge Responses": {"main": [[{"node": "Format Enhanced Response", "type": "main", "index": 0}]]}, "Format Enhanced Response": {"main": [[{"node": "Update Statistics", "type": "main", "index": 0}, {"node": "High Cost Alert?", "type": "main", "index": 0}]]}, "High Cost Alert?": {"main": [[{"node": "Cost <PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "bb8f0ad2-e9c9-4a86-aa35-43f59c16bd8d", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "T8eSJk2HCNdEy7zU", "tags": []}
{"name": "LLM Rotation Manager", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "prompt"}, {"name": "systemMessage"}, {"name": "temperature", "type": "number"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-800, 200], "id": "596072b8-4cab-48d5-b583-024e216bcf39", "name": "Workflow Input"}, {"parameters": {"documentId": {"__rl": true, "value": "YOUR_GOOGLE_SHEET_ID", "mode": "list"}, "sheetName": {"__rl": true, "value": "API_Usage", "mode": "list"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [-600, 200], "id": "33ba9fa3-4bf5-4c15-9638-5da94ee79ff6", "name": "Read API Usage Stats"}, {"parameters": {"functionCode": "// API Provider Konfiguration\nconst providers = [\n  {\n    name: 'openai-1',\n    type: 'openai',\n    model: 'gpt-3.5-turbo',\n    dailyLimit: 50,\n    monthlyLimit: 1000,\n    priority: 1\n  },\n  {\n    name: 'openai-2',\n    type: 'openai',\n    model: 'gpt-3.5-turbo',\n    dailyLimit: 50,\n    monthlyLimit: 1000,\n    priority: 2\n  },\n  {\n    name: 'anthropic-1',\n    type: 'anthropic',\n    model: 'claude-3-haiku-20240307',\n    dailyLimit: 100,\n    monthlyLimit: 2000,\n    priority: 3\n  },\n  {\n    name: 'gemini-1',\n    type: 'google',\n    model: 'gemini-1.5-flash',\n    dailyLimit: 1500,\n    monthlyLimit: 30000,\n    priority: 4\n  },\n  {\n    name: 'groq-1',\n    type: 'groq',\n    model: 'llama-3.2-90b-text-preview',\n    dailyLimit: 100,\n    monthlyLimit: 2000,\n    priority: 5\n  }\n];\n\n// Hole Usage Daten\nconst usageData = items[0].json.rows || [];\nconst today = new Date().toISOString().split('T')[0];\nconst thisMonth = new Date().toISOString().slice(0, 7);\n\n// Berechne verfügbare APIs\nconst availableProviders = providers.map(provider => {\n  const todayUsage = usageData.filter(row => \n    row.provider === provider.name && \n    row.date === today\n  ).reduce((sum, row) => sum + (row.count || 0), 0);\n  \n  const monthUsage = usageData.filter(row => \n    row.provider === provider.name && \n    row.date.startsWith(thisMonth)\n  ).reduce((sum, row) => sum + (row.count || 0), 0);\n  \n  const dailyRemaining = provider.dailyLimit - todayUsage;\n  const monthlyRemaining = provider.monthlyLimit - monthUsage;\n  \n  return {\n    ...provider,\n    todayUsage,\n    monthUsage,\n    dailyRemaining,\n    monthlyRemaining,\n    isAvailable: dailyRemaining > 0 && monthlyRemaining > 0,\n    score: dailyRemaining / provider.dailyLimit * 100 + provider.priority\n  };\n});\n\n// Sortiere nach Verfügbarkeit und Score\nconst selectedProvider = availableProviders\n  .filter(p => p.isAvailable)\n  .sort((a, b) => b.score - a.score)[0];\n\nif (!selectedProvider) {\n  throw new Error('Keine API verfügbar! Alle Limits erreicht.');\n}\n\nreturn [\n  {\n    json: {\n      selectedProvider,\n      prompt: items[0].json.prompt,\n      systemMessage: items[0].json.systemMessage,\n      temperature: items[0].json.temperature,\n      timestamp: new Date().toISOString()\n    }\n  }\n];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-400, 200], "id": "7b1f856a-080f-4c31-8664-778baa81a333", "name": "Select Best Provider"}, {"parameters": {"rules": {"values": [{"conditions": {"conditions": [{"leftValue": "={{ $json.selectedProvider.type }}", "rightValue": "openai", "operator": {"type": "string", "operation": "equals"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.selectedProvider.type }}", "rightValue": "anthropic", "operator": {"type": "string", "operation": "equals"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.selectedProvider.type }}", "rightValue": "google", "operator": {"type": "string", "operation": "equals"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.selectedProvider.type }}", "rightValue": "groq", "operator": {"type": "string", "operation": "equals"}}]}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-200, 200], "id": "6558d18b-1be1-440f-92e5-69d54d05bce3", "name": "Route to Provider"}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": ""}, "messages": {"values": [{"content": "={{ $json.systemMessage }}", "role": "system"}, {"content": "={{ $json.prompt }}"}]}, "options": {"maxTokens": 2000, "temperature": "={{ $json.temperature }}"}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-20, -40], "id": "1debdbc4-9cf0-40ba-9344-8874a5de7f3c", "name": "OpenAI"}, {"parameters": {"model": "={{ $json.selectedProvider.model }}", "options": {"temperature": "={{ $json.temperature }}"}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.2, "position": [0, 160], "id": "8af7af32-a8d7-41f5-a38f-3038f62648de", "name": "Anthropic"}, {"parameters": {"assignments": {"assignments": [{"name": "response", "value": "={{ $json.message.content }}", "type": "string"}, {"name": "provider", "value": "={{ $('Select Best Provider').item.json.selectedProvider.name }}", "type": "string"}, {"name": "model", "value": "={{ $('Select Best Provider').item.json.selectedProvider.model }}", "type": "string"}, {"name": "timestamp", "value": "={{ $now }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [400, 200], "id": "d4a96f69-7643-48bf-8dd6-d0c1bb93137e", "name": "Format Response"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "YOUR_GOOGLE_SHEET_ID", "mode": "list"}, "sheetName": {"__rl": true, "value": "API_Usage", "mode": "list"}, "columns": {"value": {"provider": "={{ $json.provider }}", "date": "={{ $now.format('yyyy-MM-dd') }}", "count": 1, "timestamp": "={{ $json.timestamp }}", "tokens_used": "={{ $json.usage?.totalTokens || 0 }}"}}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [600, 200], "id": "f5d4066f-25b7-4ed2-9fb5-5924582f80fa", "name": "Log API Usage"}, {"parameters": {"assignments": {"assignments": [{"name": "error", "value": "Provider {{ $json.selectedProvider.name }} failed: {{ $json.error.message }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [200, 400], "id": "5bd3244a-f13c-4fe6-ae72-783570477408", "name": "<PERSON><PERSON><PERSON>"}], "pinData": {}, "connections": {"Workflow Input": {"main": [[{"node": "Read API Usage Stats", "type": "main", "index": 0}]]}, "Read API Usage Stats": {"main": [[{"node": "Select Best Provider", "type": "main", "index": 0}]]}, "Select Best Provider": {"main": [[{"node": "Route to Provider", "type": "main", "index": 0}]]}, "Route to Provider": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]]}, "Format Response": {"main": [[{"node": "Log API Usage", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "ffb21879-642d-48ad-9746-3bc3e0a432e8", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "LHUCVlAtZjqCKWMp", "tags": []}
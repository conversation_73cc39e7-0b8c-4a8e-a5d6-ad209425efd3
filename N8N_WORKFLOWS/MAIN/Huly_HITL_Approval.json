{"name": "Huly HITL Approval", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "approval_type"}, {"name": "project_id"}, {"name": "huly_project_id"}, {"name": "huly_task_id"}, {"name": "details"}, {"name": "urgency"}, {"name": "approver"}, {"name": "deadline"}, {"name": "project_value", "type": "number"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-240, -20], "id": "62303485-1e2f-4088-82bf-d6156baed80e", "name": "✋ HITL Approval Trigger"}, {"parameters": {"method": "POST", "url": "{{ $env.HULY_API_URL }}/api/issues", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"workspace\": \"{{ $env.HULY_WORKSPACE_ID }}\",\n  \"project\": \"{{ $json.huly_project_id }}\",\n  \"issue\": {\n    \"title\": \"🔴 FREIGABE: {{ $json.approval_type }} - {{ $json.project_id }}\",\n    \"description\": \"**Freigabe erforderlich für Bauprojekt**\\n\\n**📋 Details:**\\n{{ $json.details }}\\n\\n**⚡ Dringlichkeit:** {{ $json.urgency }}\\n**👤 Genehmiger:** {{ $json.approver }}\\n**⏰ Frist:** {{ $json.deadline }}\\n{{ $json.project_value ? '**💰 Wert:** €' + $json.project_value : '' }}\\n\\n**🔗 Verknüpfte Task:** [Task öffnen]({{ $env.HULY_BASE_URL }}/task/{{ $json.huly_task_id }})\",\n    \"status\": \"pending_approval\",\n    \"priority\": \"{{ $json.urgency === 'urgent' ? 'urgent' : $json.urgency === 'high' ? 'high' : 'normal' }}\",\n    \"assignee\": \"{{ $json.approver }}\",\n    \"labels\": [\"hitl-approval\", \"{{ $json.approval_type }}\", \"bauprozess\"],\n    \"dueDate\": \"{{ $json.deadline }}\",\n    \"customFields\": {\n      \"approvalType\": \"{{ $json.approval_type }}\",\n      \"originalProjectId\": \"{{ $json.project_id }}\",\n      \"linkedTask\": \"{{ $json.huly_task_id }}\",\n      \"projectValue\": \"{{ $json.project_value }}\",\n      \"telegramApproval\": true,\n      \"autoApproveAfter\": \"{{ $json.deadline }}\"\n    }\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-40, -20], "id": "257bd417-ffbd-41b4-afc2-41fa62642412", "name": "📝 Create Hu<PERSON> Approval Issue"}, {"parameters": {"operation": "append"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [160, -20], "id": "72219377-a90d-4df5-80eb-dbe8b44786b7", "name": "📊 Log Approval Request"}, {"parameters": {"operation": "send"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [360, -20], "id": "bf40e1f5-466c-45d5-8d3b-e861e7ff9667", "name": "📱 Send Telegram Notification", "webhookId": "a592686f-f9cd-48c9-a36b-503ab11c8e85"}, {"parameters": {"updates": ["callback_query"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-240, 280], "id": "3398f53f-fef9-4341-b83b-240f1615900e", "name": "📱 Telegram Callback Handler", "webhookId": "c68f5210-5a3d-49d0-bee8-152938f09977"}, {"parameters": {"rules": {"values": [{"conditions": {"conditions": [{"leftValue": "={{ $json.callback_query?.data }}", "rightValue": "approve_", "operator": {"type": "string", "operation": "startsWith"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.callback_query?.data }}", "rightValue": "reject_", "operator": {"type": "string", "operation": "startsWith"}}]}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-40, 280], "id": "d1b464c6-37f9-4e60-973a-e83e0427c253", "name": "🔀 Route Callback"}, {"parameters": {"operation": "update", "tableId": "hitl_approvals", "filterType": "string", "fieldsUi": {"fieldValues": [{"fieldValue": "approved"}, {"fieldValue": "={{ $json.callback_query.from.username }}"}, {"fieldValue": "={{ $now }}"}, {"fieldValue": "telegram"}, {"fieldValue": "Genehmigt via Telegram"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [160, 180], "id": "9418308f-c49d-4749-a733-100eb148a541", "name": "✅ Approve in Database"}, {"parameters": {"operation": "update", "tableId": "hitl_approvals", "filterType": "string", "fieldsUi": {"fieldValues": [{"fieldValue": "rejected"}, {"fieldValue": "={{ $json.callback_query.from.username }}"}, {"fieldValue": "={{ $now }}"}, {"fieldValue": "telegram"}, {"fieldValue": "Abgelehnt via Telegram"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [160, 380], "id": "3aa2df38-64f0-4ac0-8558-f65a858df02e", "name": "❌ Reject in Database"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "=SELECT * FROM hitl_approvals WHERE approval_id = '{{ $json.callback_query.data.split('_')[1] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [360, 280], "id": "b97b2ed1-6497-487b-8e9f-da5f845963c2", "name": "📋 Get Approval Details"}, {"parameters": {"method": "PATCH", "url": "={{ $env.HULY_API_URL }}/api/issues/{{ $json.huly_issue_id }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"status\": \"{{ $('✅ Approve in Database').item.json ? 'approved' : 'rejected' }}\",\n  \"resolution\": \"{{ $('✅ Approve in Database').item.json ? 'approved' : 'rejected' }}\",\n  \"assignee\": \"{{ $json.callback_query.from.username }}\",\n  \"comments\": [\n    {\n      \"author\": \"{{ $json.callback_query.from.username }}\",\n      \"content\": \"{{ $('✅ Approve in Database').item.json ? '✅ Genehmigt' : '❌ Abgelehnt' }} via Telegram am {{ $now.format('dd.MM.yyyy HH:mm') }}\",\n      \"timestamp\": \"{{ $now }}\"\n    }\n  ],\n  \"customFields\": {\n    \"approvalMethod\": \"telegram\",\n    \"approvedBy\": \"{{ $json.callback_query.from.username }}\",\n    \"approvalTimestamp\": \"{{ $now }}\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [560, 280], "id": "0047f1ac-5952-4cba-8173-2447bff26bb5", "name": "🔄 Update Huly Issue"}, {"parameters": {"method": "PATCH", "url": "={{ $env.HULY_API_URL }}/api/tasks/{{ $json.huly_task_id }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"status\": \"{{ $('✅ Approve in Database').item.json ? 'approved' : 'blocked' }}\",\n  \"comments\": [\n    {\n      \"author\": \"system\",\n      \"content\": \"HITL Freigabe {{ $('✅ Approve in Database').item.json ? '✅ erteilt' : '❌ verweigert' }} von {{ $json.callback_query.from.username }}\",\n      \"timestamp\": \"{{ $now }}\"\n    }\n  ],\n  \"customFields\": {\n    \"hitlStatus\": \"{{ $('✅ Approve in Database').item.json ? 'approved' : 'rejected' }}\",\n    \"hitlApprovedBy\": \"{{ $json.callback_query.from.username }}\",\n    \"hitlTimestamp\": \"{{ $now }}\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [760, 280], "id": "7aed7dfe-a513-4c0f-81e0-d29d2e5be3b6", "name": "📋 Update Huly Task"}, {"parameters": {"workflowId": {"__rl": true, "value": "jarvis-huly-construction-workflow", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.1, "position": [960, 280], "id": "390c11cf-7377-46fa-9a07-78284d38d121", "name": "🔄 Continue Construction Process"}, {"parameters": {"method": "POST", "url": "https://api.telegram.org/bot{{ $env.TELEGRAM_BOT_TOKEN }}/editMessageText", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"chat_id\": {{ $json.callback_query.message.chat.id }},\n  \"message_id\": {{ $json.callback_query.message.message_id }},\n  \"text\": \"{{ $('✅ Approve in Database').item.json ? '✅ GENEHMIGT' : '❌ ABGELEHNT' }}\\n\\n🏗️ **Projekt:** {{ $json.project_id }}\\n👤 **Von:** {{ $json.callback_query.from.username }}\\n⏰ **Am:** {{ $now.format('dd.MM.yyyy HH:mm') }}\\n\\n🔗 **Huly Links:**\\n• [Issue]({{ $env.HULY_BASE_URL }}/issue/{{ $json.huly_issue_id }})\\n• [Task]({{ $env.HULY_BASE_URL }}/task/{{ $json.huly_task_id }})\\n• [Projekt]({{ $env.HULY_BASE_URL }}/project/{{ $json.huly_project_id }})\\n\\n{{ $('✅ Approve in Database').item.json ? '🔄 Prozess wird fortgesetzt...' : '⚠️ Prozess pausiert - manuelle Überprüfung erforderlich' }}\",\n  \"parse_mode\": \"Markdown\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1160, 280], "id": "1441363b-08ce-4f1f-aaee-3a642a5f3cc9", "name": "📝 Update Telegram Message"}, {"parameters": {"sendTo": "={{ $env.PROJECT_MANAGER_EMAIL }}", "subject": "=Huly HITL: {{ $('✅ Approve in Database').item.json ? 'Freigabe erteilt' : 'Freigabe verweigert' }} - {{ $json.project_id }}", "message": "=Hallo Team,\n\ndie HITL-Freigabe für Projekt **{{ $json.project_id }}** wurde {{ $('✅ Approve in Database').item.json ? 'erteilt' : 'verweigert' }}.\n\n**📋 Details:**\n- **Projekt:** {{ $json.project_id }}\n- **Freigabe-Typ:** {{ $json.approval_type }}\n- **<PERSON><PERSON><PERSON>:** {{ $json.callback_query.from.username }}\n- **Zeitpunkt:** {{ $now.format('dd.MM.yyyy HH:mm') }}\n- **Status:** {{ $('✅ Approve in Database').item.json ? '✅ Genehmigt' : '❌ Abgelehnt' }}\n\n**🔗 Huly Links:**\n- [Approval Issue öffnen]({{ $env.HULY_BASE_URL }}/issue/{{ $json.huly_issue_id }})\n- [Projekt Dashboard]({{ $env.HULY_BASE_URL }}/project/{{ $json.huly_project_id }})\n- [Related Task]({{ $env.HULY_BASE_URL }}/task/{{ $json.huly_task_id }})\n\n{{ $('✅ Approve in Database').item.json ? '🔄 Der Bauprozess wird automatisch fortgesetzt.' : '⚠️ Der Prozess ist pausiert und benötigt manuelle Überprüfung.' }}\n\nBeste Grüße,\nJarvis Construction System", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [1360, 280], "id": "97bdb9f1-bcc6-4ad5-a63d-30f6e3a824f7", "name": "📧 Notify Team via Email", "webhookId": "a1e5c659-1f5e-4680-a1e1-bea202d7756c"}, {"parameters": {"operation": "append"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1560, 280], "id": "c933397f-d079-484b-903a-c31e1ec99bc3", "name": "📋 <PERSON>t Log"}], "pinData": {}, "connections": {"✋ HITL Approval Trigger": {"main": [[{"node": "📝 Create Hu<PERSON> Approval Issue", "type": "main", "index": 0}]]}, "📝 Create Huly Approval Issue": {"main": [[{"node": "📊 Log Approval Request", "type": "main", "index": 0}]]}, "📊 Log Approval Request": {"main": [[{"node": "📱 Send Telegram Notification", "type": "main", "index": 0}]]}, "📱 Telegram Callback Handler": {"main": [[{"node": "🔀 Route Callback", "type": "main", "index": 0}]]}, "🔀 Route Callback": {"main": [[{"node": "✅ Approve in Database", "type": "main", "index": 0}], [{"node": "❌ Reject in Database", "type": "main", "index": 0}]]}, "✅ Approve in Database": {"main": [[{"node": "📋 Get Approval Details", "type": "main", "index": 0}]]}, "❌ Reject in Database": {"main": [[{"node": "📋 Get Approval Details", "type": "main", "index": 0}]]}, "📋 Get Approval Details": {"main": [[{"node": "🔄 Update Huly Issue", "type": "main", "index": 0}, {"node": "📋 Update Huly Task", "type": "main", "index": 0}]]}, "🔄 Update Huly Issue": {"main": [[{"node": "🔄 Continue Construction Process", "type": "main", "index": 0}]]}, "📋 Update Huly Task": {"main": [[{"node": "📝 Update Telegram Message", "type": "main", "index": 0}]]}, "📝 Update Telegram Message": {"main": [[{"node": "📋 <PERSON>t Log", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "70446bdd-6ec8-4425-a572-9fad61fc8b35", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "ig8C9uwVLDr318et", "tags": []}
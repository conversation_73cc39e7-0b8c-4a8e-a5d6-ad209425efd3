{"name": "Jarvis Context Manager Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "context/update", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1420, 60], "id": "2c43fe60-6903-4ea9-ae6d-6423e379aa90", "name": "Context Update Webhook", "webhookId": "jarvis-context-manager"}, {"parameters": {"assignments": {"assignments": [{"id": "user_id", "name": "user_id", "value": "={{ $json.body.user_id || 'default_user' }}", "type": "string"}, {"id": "agent", "name": "agent", "value": "={{ $json.body.agent }}", "type": "string"}, {"id": "context_type", "name": "context_type", "value": "={{ $json.body.context_type }}", "type": "string"}, {"id": "context_data", "name": "context_data", "value": "={{ $json.body.context_data }}", "type": "object"}, {"id": "timestamp", "name": "timestamp", "value": "={{ $now }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1220, 60], "id": "7ca9db8a-2293-4166-b307-644f82432b59", "name": "Parse Context Data"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "=-- Hole aktuellen Context\nSELECT \n  context_data,\n  created_at,\n  agent,\n  context_type\nFROM user_context \nWHERE user_id = '{{ $json.user_id }}'\n  AND created_at > NOW() - INTERVAL '24 hours'\nORDER BY created_at DESC\nLIMIT 50", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1020, 60], "id": "2b73a1b3-5aa2-45d0-a869-f05f4874be95", "name": "Get Existing Context"}, {"parameters": {"jsCode": "// Context Aggregation & Intelligence\nconst newContext = $('Parse Context Data').first().json;\nconst existingContext = $('Get Existing Context').all();\n\n// Aggregiere verschiedene Context-Typen\nconst contextTypes = {\n  'calendar': [],\n  'email': [],\n  'tasks': [],\n  'communication': [],\n  'research': [],\n  'personal': []\n};\n\n// Verarbeite bestehenden Context\nexistingContext.forEach(ctx => {\n  const type = ctx.json.context_type || 'general';\n  if (contextTypes[type]) {\n    contextTypes[type].push({\n      data: ctx.json.context_data,\n      timestamp: ctx.json.created_at,\n      agent: ctx.json.agent\n    });\n  }\n});\n\n// Füge neuen Context hinzu\nconst newType = newContext.context_type || 'general';\nif (contextTypes[newType]) {\n  contextTypes[newType].unshift({\n    data: newContext.context_data,\n    timestamp: newContext.timestamp,\n    agent: newContext.agent\n  });\n}\n\n// Generiere Intelligence Insights\nconst insights = {\n  recent_activity: {\n    last_email: contextTypes.email[0]?.timestamp || null,\n    last_calendar: contextTypes.calendar[0]?.timestamp || null,\n    last_task: contextTypes.tasks[0]?.timestamp || null,\n    active_agents: [...new Set(existingContext.map(c => c.json.agent))]\n  },\n  patterns: {\n    most_active_agent: existingContext.reduce((acc, curr) => {\n      acc[curr.json.agent] = (acc[curr.json.agent] || 0) + 1;\n      return acc;\n    }, {}),\n    context_frequency: Object.keys(contextTypes).map(type => ({\n      type,\n      count: contextTypes[type].length\n    }))\n  },\n  urgency_indicators: {\n    pending_tasks: contextTypes.tasks.filter(t => \n      t.data?.status === 'pending' || t.data?.priority === 'high'\n    ).length,\n    unread_emails: contextTypes.email.filter(e => \n      e.data?.status === 'unread'\n    ).length,\n    upcoming_meetings: contextTypes.calendar.filter(c => \n      new Date(c.data?.start) > new Date() && \n      new Date(c.data?.start) < new Date(Date.now() + 24*60*60*1000)\n    ).length\n  }\n};\n\n// Erstelle Enhanced Context\nconst enhancedContext = {\n  user_id: newContext.user_id,\n  timestamp: newContext.timestamp,\n  context_types: contextTypes,\n  insights,\n  summary: {\n    total_contexts: existingContext.length + 1,\n    active_agents: insights.recent_activity.active_agents.length,\n    urgency_score: insights.urgency_indicators.pending_tasks * 3 + \n                   insights.urgency_indicators.unread_emails * 1 + \n                   insights.urgency_indicators.upcoming_meetings * 2\n  },\n  recommendations: []\n};\n\n// Generiere Empfehlungen basierend auf Context\nif (insights.urgency_indicators.pending_tasks > 5) {\n  enhancedContext.recommendations.push({\n    type: 'task_management',\n    message: 'Viele offene Aufgaben - soll ich priorisieren?',\n    action: 'prioritize_tasks'\n  });\n}\n\nif (insights.urgency_indicators.upcoming_meetings > 0) {\n  enhancedContext.recommendations.push({\n    type: 'meeting_prep',\n    message: `${insights.urgency_indicators.upcoming_meetings} Meeting(s) heute - Vorbereitung benötigt?`,\n    action: 'prepare_meetings'\n  });\n}\n\nreturn [{ json: enhancedContext }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-820, 60], "id": "1eda728c-06f0-4a7b-81a0-6a755e9c3e72", "name": "🧠 Context Intelligence Engine"}, {"parameters": {"operation": "upsert"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-620, 260], "id": "52150f0f-48f6-48a0-90f7-19cd6daeefef", "name": "💾 Store Context"}, {"parameters": {"operation": "upsert"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-620, 60], "id": "ce672ea4-c5bf-4f46-b1a2-6bd84d28dc02", "name": "🚀 Store Enhanced Context"}, {"parameters": {"conditions": {"conditions": [{"leftValue": "={{ $json.summary.urgency_score }}", "rightValue": 10, "operator": {"type": "number", "operation": "larger"}}]}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-420, 60], "id": "4f4c9cb0-16fb-4c38-a139-9de65643aafb", "name": "⚡ High Urgency?"}, {"parameters": {"operation": "send"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-220, -40], "id": "8469f059-f32c-4c0f-95af-07a4256ae4ee", "name": "📱 Urgent Notification", "webhookId": "b88a6bc8-3d8d-4e78-ab25-862a96004055"}, {"parameters": {"path": "context/get", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1420, 360], "id": "7e2cd1a5-a145-4430-b5bb-f3da65676cb4", "name": "Get Context Webhook", "webhookId": "jarvis-get-context"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "=-- Hole Enhanced Context für User\nSELECT \n  enhanced_context,\n  urgency_score,\n  last_updated\nFROM enhanced_context \nWHERE user_id = '{{ $json.query.user_id || 'default_user' }}'\nORDER BY last_updated DESC\nLIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1220, 360], "id": "815722bc-de75-469a-b92b-3b7d61484e23", "name": "📖 Retrieve Context"}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [-1020, 360], "id": "a6aa7e64-ed2d-4f25-8c7c-39be82fb5b81", "name": "↩️ Return Context"}, {"parameters": {"content": "## Needed Daatabase Tables \n\n\n\n-- Context Storage\nCREATE TABLE user_context (\n    id SERIAL PRIMARY KEY,\n    user_id VARCHAR(50) NOT NULL,\n    agent VARCHAR(100) NOT NULL,\n    context_type VARCHAR(50) NOT NULL,\n    context_data JSONB NOT NULL,\n    created_at TIMESTAMP DEFAULT NOW()\n);\n\n-- Enhanced Context Cache\nCREATE TABLE enhanced_context (\n    id SERIAL PRIMARY KEY,\n    user_id VARCHAR(50) UNIQUE NOT NULL,\n    enhanced_context JSONB NOT NULL,\n    urgency_score INTEGER DEFAULT 0,\n    total_contexts INTEGER DEFAULT 0,\n    active_agents INTEGER DEFAULT 0,\n    last_updated TIMESTAMP DEFAULT NOW()\n);\n\nCREATE INDEX idx_user_context_user_time ON user_context(user_id, created_at);\nCREATE INDEX idx_enhanced_context_urgency ON enhanced_context(urgency_score);", "height": 260, "width": 320}, "type": "n8n-nodes-base.stickyNote", "position": [-1660, -340], "typeVersion": 1, "id": "e02af231-237b-41ce-8322-3b8b2289b856", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"Context Update Webhook": {"main": [[{"node": "Parse Context Data", "type": "main", "index": 0}]]}, "Parse Context Data": {"main": [[{"node": "Get Existing Context", "type": "main", "index": 0}]]}, "Get Existing Context": {"main": [[{"node": "🧠 Context Intelligence Engine", "type": "main", "index": 0}]]}, "🧠 Context Intelligence Engine": {"main": [[{"node": "💾 Store Context", "type": "main", "index": 0}, {"node": "🚀 Store Enhanced Context", "type": "main", "index": 0}]]}, "🚀 Store Enhanced Context": {"main": [[{"node": "⚡ High Urgency?", "type": "main", "index": 0}]]}, "⚡ High Urgency?": {"main": [[{"node": "📱 Urgent Notification", "type": "main", "index": 0}]]}, "Get Context Webhook": {"main": [[{"node": "📖 Retrieve Context", "type": "main", "index": 0}]]}, "📖 Retrieve Context": {"main": [[{"node": "↩️ Return Context", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "e8d97eb3-655b-4891-bb0b-a0b34a521e68", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "pAaMR39dodlHtx2I", "tags": []}
{"name": "Jarvis Orchestrator Main Workflow", "nodes": [{"parameters": {"events": [{"eventName": "workflow.success"}, {"eventName": "workflow.failed"}, {"eventName": "workflow.activated"}, {"eventName": "workflow.updated"}]}, "type": "n8n-nodes-base.n8nTrigger", "typeVersion": 1, "position": [-520, -80], "id": "02158e55-faab-4241-9097-fcdcc3bac4c6", "name": "Workflow Event Monitor"}, {"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-520, 120], "id": "4a7e799e-409b-4a2f-a98b-c3a79f576ebe", "name": "System Heartbeat"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM system_logs WHERE timestamp > NOW() - INTERVAL '1 minute' ORDER BY timestamp DESC", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-320, 120], "id": "9ae36ac9-cc07-4ffa-b036-0a4325376466", "name": "Fetch Recent Logs"}, {"parameters": {"promptType": "define", "text": "={{ JSON.stringify($json) }}", "options": {"systemMessage": "=Du bist der zentrale Orchestrator des Jarvis-Systems. Deine Aufgaben:\n\n1. **Überwachung**: Analy<PERSON><PERSON> kontinui<PERSON>lich alle System-Events, Logs und Agent-Aktivitäten\n2. **Anomalie-Erkennung**: Identifiziere ungewöhn<PERSON> Muster, Fehler oder Engpässe\n3. **Priorisierung**: Bewerte die Wichtigkeit von Events (kritisch/hoch/mittel/niedrig)\n4. **Intervention**: Entscheide, wann du eingreifen musst\n5. **Koordination**: Orchestriere die Zusammenarbeit zwischen Agents\n\nDu hast Zugriff auf:\n- Alle Workflow-Logs und Metriken\n- Agent-Status und Auslastung\n- Systemressourcen und Performance\n- Historische Daten und Muster\n- Notfall-Interventionsmöglichkeiten\n\nAntworte im JSON-Format:\n{\n  \"analysis\": \"Kurze Situationsanalyse\",\n  \"priority\": \"critical|high|medium|low\",\n  \"anomalies\": [\"Liste von Anomalien\"],\n  \"recommendations\": [\"Empfohlene Aktionen\"],\n  \"interventions\": [\"Notwendige Eingriffe\"],\n  \"agent_coordination\": {\"agent\": \"action\"}\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [140, -320], "id": "2cce932b-9050-4e77-8320-6e62a440dab5", "name": "Orchestrator AI Brain"}, {"parameters": {"rules": {"values": [{"conditions": {"conditions": [{"leftValue": "={{ $json.priority }}", "rightValue": "critical", "operator": {"type": "string", "operation": "equals"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.priority }}", "rightValue": "high", "operator": {"type": "string", "operation": "equals"}}]}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [280, 20], "id": "11b0251e-6329-4384-b9a0-c05a5281639f", "name": "Priority Router"}, {"parameters": {"operation": "send"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [480, -60], "id": "71a8a67e-19af-4a15-9ba0-afe052e77f7a", "name": "Critical Alert", "webhookId": "orchestrator-alerts"}, {"parameters": {"tableName": {"__rl": true, "value": "orchestrator_memory", "mode": "list"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [-120, 220], "id": "8cab1aca-542d-4659-8d12-f9b6223e0e58", "name": "Long Term Memory"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "orchestrator-main", "contextWindowLength": 50}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [280, 220], "id": "2a98a6a7-d3db-4a47-9522-2e1d4dc2c5b6", "name": "Context Memory"}, {"parameters": {"name": "agent_controller", "description": "Steuere und koordiniere andere Agents", "workflowId": {"__rl": true, "value": "{{ $env.AGENT_CONTROLLER_WORKFLOW }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"action": "={{ $fromAI('action') }}", "agent": "={{ $fromAI('target_agent') }}", "parameters": "={{ $fromAI('parameters') }}"}}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [480, 220], "id": "2c69780b-6475-404f-9260-52773cac6058", "name": "Agent Controller"}, {"parameters": {"name": "system_diagnostics", "description": "Führe System-Diagnosen und Health-Checks durch", "workflowId": {"__rl": true, "value": "{{ $env.DIAGNOSTICS_WORKFLOW }}", "mode": "id"}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [680, 220], "id": "a5d80bc6-8312-43d4-ac76-e2e00af3aec8", "name": "System Diagnostics"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "=-- Hole Agent-Status und Metriken\nSELECT \n  agent_name,\n  status,\n  last_activity,\n  error_count,\n  avg_response_time,\n  current_load\nFROM agent_metrics\nWHERE last_update > NOW() - INTERVAL '5 minutes'\nORDER BY error_count DESC, current_load DESC", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-120, 320], "id": "d09527df-2b84-4892-944d-ef294b4d9aa7", "name": "Get Agent Status"}, {"parameters": {"mode": "combine", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [80, 220], "id": "0dbdf989-e66f-4fb9-a7ff-be78580c5639", "name": "Merge Context Data"}, {"parameters": {"operation": "append"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [880, 20], "id": "da9a4b56-0e8a-4cd1-bf08-1cb149ccb14b", "name": "Log Orchestrator Activity"}, {"parameters": {"operation": "update", "updateFields": {}}, "type": "n8n-nodes-base.grafana", "typeVersion": 1, "position": [1080, 120], "id": "87b0635d-18ef-4d2d-883d-c26d60de107a", "name": "Update Dashboard"}, {"parameters": {"model": "gpt-4", "options": {"temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [280, 320], "id": "dc7d38fa-db68-4263-be54-6350baa0b18e", "name": "Orchestrator LLM"}, {"parameters": {"content": "## 🧠 Jarvis Orchestrator - Central Intelligence\n\nThis workflow is the brain of the system:\n\n### Main functions:\n1. **Continuous monitoring** of all system events\n2. **Anomaly detection** and pattern recognition\n3. **Prioritization** of events and actions\n4. **Intervention** in critical situations\n5. **Agent coordination** for complex tasks\n\n### Data sources:\n- Workflow events (success/failure)\n- System logs (PostgreSQL)\n- Agent metrics and status\n- Long-term memory (Supabase)\n- Real-time context (Buffer Memory)\n\n### Intervention options:\n- Critical alerts via Telegram\n- Agent control and restarts\n- System diagnostics\n- Dashboard updates\n- Emergency workflows", "height": 620, "width": 480}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-440, -780], "id": "8ed467cb-5cca-48cf-ac56-fff90351a151", "name": "Orchestrator Overview"}], "pinData": {}, "connections": {"Workflow Event Monitor": {"main": [[{"node": "Merge Context Data", "type": "main", "index": 0}]]}, "System Heartbeat": {"main": [[{"node": "Fetch Recent Logs", "type": "main", "index": 0}, {"node": "Get Agent Status", "type": "main", "index": 0}]]}, "Fetch Recent Logs": {"main": [[{"node": "Merge Context Data", "type": "main", "index": 1}]]}, "Merge Context Data": {"main": [[{"node": "Orchestrator AI Brain", "type": "main", "index": 0}]]}, "Orchestrator AI Brain": {"main": [[{"node": "Priority Router", "type": "main", "index": 0}, {"node": "Log Orchestrator Activity", "type": "main", "index": 0}, {"node": "Update Dashboard", "type": "main", "index": 0}]]}, "Priority Router": {"main": [[{"node": "Critical Alert", "type": "main", "index": 0}]]}, "Orchestrator LLM": {"ai_languageModel": [[{"node": "Orchestrator AI Brain", "type": "ai_languageModel", "index": 0}]]}, "Context Memory": {"ai_memory": [[{"node": "Orchestrator AI Brain", "type": "ai_memory", "index": 0}]]}, "Agent Controller": {"ai_tool": [[{"node": "Orchestrator AI Brain", "type": "ai_tool", "index": 0}]]}, "System Diagnostics": {"ai_tool": [[{"node": "Orchestrator AI Brain", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "853111ff-9429-4ecc-a4b3-f43dff0b491f", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "p5FeCYgofcKADm5O", "tags": []}
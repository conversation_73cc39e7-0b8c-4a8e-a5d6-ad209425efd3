{"name": "Jarvis Task Router", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "task"}, {"name": "type"}, {"name": "priority"}, {"name": "userId"}, {"name": "context"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1260, 600], "id": "d7a44856-7f16-4dfe-8704-49f8c3911971", "name": "Task Input"}, {"parameters": {"promptType": "define", "text": "={{ JSON.stringify($json) }}", "options": {"systemMessage": "=Du bist der Task Router für das Jarvis System. Analysiere die eingehende Aufgabe und entscheide:\n\n1. **Welcher Agent** ist am besten geeignet\n2. **Priorität** der Aufgabe (critical/high/medium/low)\n3. **Geschätzte Dauer** \n4. **Benötigte Ressourcen**\n\n**Verfügbare Agents:**\n- email_agent: E-Mail Management (Lesen, Schreiben, Organisieren)\n- calendar_agent: Terminverwaltung (Erstellen, Verschieben, Konfliktlösung)\n- call_agent: Anrufverwaltung (Tätigen, Planen, Transkribieren)\n- research_agent: Recherche & Analyse (Web, Dokumente, Daten)\n- automation_agent: Workflow-Erstellung & Automatisierung\n- finance_agent: Finanzen & Buchhaltung\n- social_agent: Social Media Management\n- file_agent: Dateiverwaltung (Drive, Dropbox, etc.)\n- crm_agent: Kontaktverwaltung & CRM\n- dev_agent: Code & Development Tasks\n\n**Multi-Agent Tasks:**\nBei komplexen Aufgaben, die mehrere Agents benötigen, erstelle einen Execution Plan.\n\n**Output Format:**\n{\n  \"primary_agent\": \"agent_name\",\n  \"secondary_agents\": [\"agent1\", \"agent2\"],\n  \"priority\": \"critical|high|medium|low\",\n  \"estimated_duration\": \"Xmin\",\n  \"execution_plan\": [\n    {\n      \"step\": 1,\n      \"agent\": \"agent_name\",\n      \"action\": \"description\",\n      \"dependencies\": []\n    }\n  ],\n  \"resources_needed\": [\"resource1\", \"resource2\"],\n  \"parallel_execution\": true|false\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-1020, 600], "id": "f30be18f-2278-4a0a-b172-05c22e4d65e9", "name": "Router <PERSON>"}, {"parameters": {"jsCode": "// Parse Router Decision\nconst decision = JSON.parse($input.first().json.output);\nconst agents = [];\n\n// Primary Agent\nagents.push({\n  agent: decision.primary_agent,\n  priority: decision.priority,\n  isPrimary: true,\n  task: $('Task Input').first().json.task,\n  context: $('Task Input').first().json.context,\n  userId: $('Task Input').first().json.userId\n});\n\n// Secondary Agents\nif (decision.secondary_agents && decision.secondary_agents.length > 0) {\n  decision.secondary_agents.forEach(agent => {\n    agents.push({\n      agent: agent,\n      priority: 'medium',\n      isPrimary: false,\n      task: $('Task Input').first().json.task,\n      context: $('Task Input').first().json.context,\n      userId: $('Task Input').first().json.userId\n    });\n  });\n}\n\n// Add execution plan to each agent\nagents.forEach(agent => {\n  agent.executionPlan = decision.execution_plan;\n  agent.parallelExecution = decision.parallel_execution;\n});\n\nreturn agents;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-660, 600], "id": "e407dc84-a432-465a-a91c-a3c21a55b11b", "name": "Parse Router Decision"}, {"parameters": {"conditions": {"conditions": [{"leftValue": "={{ $json.agent }}", "rightValue": "email_agent", "operator": {"type": "string", "operation": "equals"}}, {"leftValue": "={{ $json.agent }}", "rightValue": "calendar_agent", "operator": {"type": "string", "operation": "equals"}}, {"leftValue": "={{ $json.agent }}", "rightValue": "call_agent", "operator": {"type": "string", "operation": "equals"}}, {"leftValue": "={{ $json.agent }}", "rightValue": "research_agent", "operator": {"type": "string", "operation": "equals"}}, {"leftValue": "={{ $json.agent }}", "rightValue": "automation_agent", "operator": {"type": "string", "operation": "equals"}}]}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-460, 600], "id": "7647c960-280e-4650-884b-bc97f49f2b35", "name": "Filter Active Agents"}, {"parameters": {"rules": {"values": [{"conditions": {"conditions": [{"leftValue": "={{ $json.agent }}", "rightValue": "email_agent", "operator": {"type": "string", "operation": "equals"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.agent }}", "rightValue": "calendar_agent", "operator": {"type": "string", "operation": "equals"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.agent }}", "rightValue": "call_agent", "operator": {"type": "string", "operation": "equals"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.agent }}", "rightValue": "research_agent", "operator": {"type": "string", "operation": "equals"}}]}}, {"conditions": {"conditions": [{"leftValue": "={{ $json.agent }}", "rightValue": "automation_agent", "operator": {"type": "string", "operation": "equals"}}]}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-260, 600], "id": "4f4b4788-95e9-44b4-9042-9cba6a237f34", "name": "Route to Agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "email-agent-workflow", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.1, "position": [-60, 200], "id": "a7c1a079-58be-4635-adc9-1f5aea6f32be", "name": "Execute Email Agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "calendar-agent-workflow", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.1, "position": [-60, 400], "id": "8e9c5c4b-ff91-476a-8b46-b5abc431b321", "name": "Execute Calendar Agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "call-agent-workflow", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.1, "position": [-60, 600], "id": "fa33c49f-0c2f-481e-bf67-22c994cbc7a9", "name": "Execute Call Agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "research-agent-workflow", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.1, "position": [-60, 800], "id": "b6df9b5d-5f12-44ba-9cf7-cf1475a44805", "name": "Execute Research Agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "automation-agent-workflow", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.1, "position": [-60, 1000], "id": "86856390-ba02-4f4c-bfa5-e5a20139e952", "name": "Execute Automation Agent"}, {"parameters": {"mode": "combine", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [140, 600], "id": "9ab06dad-fcee-431f-abbc-caedec85bb61", "name": "Merge Agent Results"}, {"parameters": {"assignments": {"assignments": [{"id": "status", "name": "status", "value": "success", "type": "string"}, {"id": "message", "name": "message", "value": "Task wurde an {{ $('Router Brain').item.json.primary_agent }} delegiert", "type": "string"}, {"id": "results", "name": "results", "value": "={{ $json }}", "type": "object"}, {"id": "execution_time", "name": "execution_time", "value": "={{ new Date() - new Date($('Task Input').first().json.timestamp) }}ms", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [340, 600], "id": "cd8cab03-34c6-4d7f-9118-af1ea947c0c3", "name": "Format Output"}, {"parameters": {"operation": "append"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-460, 800], "id": "0a14e0bf-21ea-4d52-ac26-a2dee5f35b11", "name": "Log Task Routing"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "=-- Check agent availability and load\nSELECT \n  agent_name,\n  is_available,\n  current_load,\n  avg_response_time,\n  success_rate\nFROM agent_status\nWHERE agent_name IN ({{ $json.agents.map(a => `'${a}'`).join(',') }})\nORDER BY \n  is_available DESC,\n  current_load ASC,\n  success_rate DESC", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1020, 860], "id": "d5ae8c0b-52b2-4cd2-b08c-1fde3fcf5542", "name": "Check Agent Availability"}, {"parameters": {"model": "gpt-4", "options": {"temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-820, 820], "id": "5fd19d81-a9aa-471d-a90a-52ce03d7057d", "name": "Router LLM"}, {"parameters": {"content": "## 🚦 Task Router\n\n**Features:**\n1. Analyzes incoming tasks\n2. Selects the best agent\n3. Creates execution plans for multi-agent tasks\n4. Checks agent availability\n5. Logs all routing decisions\n\n**Supported Agents:**\n- Email Agent\n- Calendar Agent\n- Call Agent\n- Research Agent\n- Automation Agent\n- Finance Agent (Coming Soon)\n- Social Agent (Coming Soon)\n- File Agent (Coming Soon)\n- CRM Agent (Coming Soon)\n- Dev Agent (Coming Soon)", "height": 520, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1080, -20], "id": "04630e32-e6ed-42c8-a582-b43d52ecee1c", "name": "Router Overview"}], "pinData": {}, "connections": {"Task Input": {"main": [[{"node": "Router <PERSON>", "type": "main", "index": 0}, {"node": "Check Agent Availability", "type": "main", "index": 0}]]}, "Router Brain": {"main": [[{"node": "Parse Router Decision", "type": "main", "index": 0}, {"node": "Log Task Routing", "type": "main", "index": 0}]]}, "Route to Agent": {"main": [[{"node": "Execute Email Agent", "type": "main", "index": 0}], [{"node": "Execute Calendar Agent", "type": "main", "index": 0}], [{"node": "Execute Call Agent", "type": "main", "index": 0}], [{"node": "Execute Research Agent", "type": "main", "index": 0}], [{"node": "Execute Automation Agent", "type": "main", "index": 0}]]}, "Execute Email Agent": {"main": [[{"node": "Merge Agent Results", "type": "main", "index": 0}]]}, "Execute Calendar Agent": {"main": [[{"node": "Merge Agent Results", "type": "main", "index": 0}]]}, "Execute Call Agent": {"main": [[{"node": "Merge Agent Results", "type": "main", "index": 0}]]}, "Execute Research Agent": {"main": [[{"node": "Merge Agent Results", "type": "main", "index": 0}]]}, "Execute Automation Agent": {"main": [[{"node": "Merge Agent Results", "type": "main", "index": 0}]]}, "Merge Agent Results": {"main": [[{"node": "Format Output", "type": "main", "index": 0}]]}, "Router LLM": {"ai_languageModel": [[{"node": "Router <PERSON>", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0f276b51-9837-496a-8c33-665a821e6bd5", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "MR9I4b5kHBh7B1dt", "tags": []}
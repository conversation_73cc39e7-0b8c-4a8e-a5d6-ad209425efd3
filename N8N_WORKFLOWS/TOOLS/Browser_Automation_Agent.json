{"name": "Browser Automation Agent - Web Scraping & Automation", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "task_type", "type": "string"}, {"name": "task_data", "type": "object"}, {"name": "user_id", "type": "string"}, {"name": "priority", "type": "string"}, {"name": "context", "type": "object"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1200, 300], "id": "browser-agent-input", "name": "Browser Agent Input"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "web_scraping", "leftValue": "={{ $json.task_type }}", "rightValue": "web_scraping", "operator": {"type": "string", "operation": "equals"}}, {"id": "form_automation", "leftValue": "={{ $json.task_type }}", "rightValue": "form_automation", "operator": {"type": "string", "operation": "equals"}}, {"id": "screenshot_capture", "leftValue": "={{ $json.task_type }}", "rightValue": "screenshot_capture", "operator": {"type": "string", "operation": "equals"}}, {"id": "website_monitoring", "leftValue": "={{ $json.task_type }}", "rightValue": "website_monitoring", "operator": {"type": "string", "operation": "equals"}}, {"id": "pdf_generation", "leftValue": "={{ $json.task_type }}", "rightValue": "pdf_generation", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "any"}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-900, 300], "id": "browser-task-router", "name": "Browser Task Router"}, {"parameters": {"url": "={{ $('Browser Agent Input').item.json.task_data.url }}", "waitUntil": "networkidle", "options": {"waitForSelector": "={{ $('Browser Agent Input').item.json.task_data.waitForSelector || 'body' }}", "timeout": 30000, "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}}, "type": "n8n-nodes-base.playwright", "typeVersion": 1, "position": [-600, 100], "id": "playwright-browser", "name": "Playwright <PERSON><PERSON><PERSON>"}, {"parameters": {"promptType": "define", "text": "=Analyze scraped content and extract relevant information:\n\nPage Content: {{ JSON.stringify($json) }}\nTask Requirements: {{ $('Browser Agent Input').item.json.task_data }}\nExtraction Rules: {{ $('Browser Agent Input').item.json.context }}\n\nProvide structured data extraction.", "options": {"systemMessage": "Du bist der Browser Automation Agent für das Jarvis System. Deine Aufgaben:\n\n**Web Scraping:**\n- Extrahiere strukturierte Daten aus Webseiten\n- Erkenne und umgehe Anti-Bot-Maßnahmen\n- Handhabe JavaScript-Heavy Sites\n- Identifiziere relevante Datenfelder\n\n**Form Automation:**\n- Fülle Formulare automatisch aus\n- Handhabe verschiedene Input-Typen\n- Erkenne und löse Captchas\n- Validiere Eingaben vor Submission\n\n**Content Monitoring:**\n- Erkenne Änderungen auf Webseiten\n- Vergleiche mit vorherigen Versionen\n- Identifiziere wichtige Updates\n- Triggere Benachrichtigungen bei Änderungen\n\n**Data Processing:**\n- Strukturiere unstrukturierte Web-Daten\n- Bereinige und validiere extrahierte Informationen\n- Erkenne Patterns und Anomalien\n\n**Output Format:**\n{\n  \"extraction_successful\": boolean,\n  \"extracted_data\": {},\n  \"data_quality_score\": number,\n  \"elements_found\": number,\n  \"captcha_detected\": boolean,\n  \"errors_encountered\": [],\n  \"page_changes_detected\": boolean,\n  \"structured_output\": {},\n  \"next_actions\": []\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-300, 200], "id": "browser-ai-processor", "name": "Browser AI Processor"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO scraped_data (user_id, url, extracted_data, quality_score, timestamp, task_type) VALUES ($1, $2, $3, $4, NOW(), $5)", "additionalFields": {"queryParameters": "={{ [$('Browser Agent Input').item.json.user_id, $('Browser Agent Input').item.json.task_data.url, JSON.stringify($json.structured_output), $json.data_quality_score, $('Browser Agent Input').item.json.task_type] }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [0, 100], "id": "store-scraped-data", "name": "Store Scraped Data"}, {"parameters": {"operation": "create", "resource": "file", "name": "={{ 'screenshot_' + $now.toUnixInteger() + '.png' }}", "parents": {"__rl": true, "value": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms", "mode": "list", "cachedResultName": "Screenshots"}, "binaryData": true, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [0, 300], "id": "save-screenshot", "name": "Save Screenshot"}, {"parameters": {"assignments": {"assignments": [{"id": "success", "name": "success", "value": true, "type": "boolean"}, {"id": "extraction_successful", "name": "extraction_successful", "value": "={{ $('Browser AI Processor').item.json.extraction_successful }}", "type": "boolean"}, {"id": "extracted_data", "name": "extracted_data", "value": "={{ $('Browser AI Processor').item.json.structured_output }}", "type": "object"}, {"id": "data_quality_score", "name": "data_quality_score", "value": "={{ $('Browser AI Processor').item.json.data_quality_score }}", "type": "number"}, {"id": "elements_found", "name": "elements_found", "value": "={{ $('Browser AI Processor').item.json.elements_found }}", "type": "number"}, {"id": "captcha_detected", "name": "captcha_detected", "value": "={{ $('Browser AI Processor').item.json.captcha_detected }}", "type": "boolean"}, {"id": "page_url", "name": "page_url", "value": "={{ $('Browser Agent Input').item.json.task_data.url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [300, 200], "id": "format-browser-response", "name": "Format Browser Response"}], "connections": {"Browser Agent Input": {"main": [[{"node": "Browser Task Router", "type": "main", "index": 0}]]}, "Browser Task Router": {"main": [[{"node": "Playwright <PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Playwright <PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Playwright <PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Playwright <PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Playwright <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Playwright Browser": {"main": [[{"node": "Browser AI Processor", "type": "main", "index": 0}]]}, "Browser AI Processor": {"main": [[{"node": "Store Scraped Data", "type": "main", "index": 0}, {"node": "Save Screenshot", "type": "main", "index": 0}]]}, "Store Scraped Data": {"main": [[{"node": "Format Browser Response", "type": "main", "index": 0}]]}, "Save Screenshot": {"main": [[{"node": "Format Browser Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-12-29T10:00:00.000Z", "updatedAt": "2024-12-29T10:00:00.000Z", "id": "browser-automation-agent", "name": "browser-automation-agent"}], "triggerCount": 0, "updatedAt": "2024-12-29T10:00:00.000Z", "versionId": "1"}
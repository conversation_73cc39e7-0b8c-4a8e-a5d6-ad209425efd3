{
  "name": "Calendar Agent - Smart Scheduling",
  "nodes": [
    {
      "parameters": {
        "workflowInputs": {
          "values": [
            {
              "name": "task_type",
              "type": "string"
            },
            {
              "name": "task_data",
              "type": "object"
            },
            {
              "name": "user_id",
              "type": "string"
            },
            {
              "name": "priority",
              "type": "string"
            },
            {
              "name": "context",
              "type": "object"
            }
          ]
        }
      },
      "type": "n8n-nodes-base.executeWorkflowTrigger",
      "typeVersion": 1.1,
      "position": [-1200, 300],
      "id": "calendar-agent-input",
      "name": "Calendar Agent Input"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "schedule_meeting",
              "leftValue": "={{ $json.task_type }}",
              "rightValue": "schedule_meeting",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "find_availability",
              "leftValue": "={{ $json.task_type }}",
              "rightValue": "find_availability",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "reschedule_event",
              "leftValue": "={{ $json.task_type }}",
              "rightValue": "reschedule_event",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "block_focus_time",
              "leftValue": "={{ $json.task_type }}",
              "rightValue": "block_focus_time",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "check_conflicts",
              "leftValue": "="{{ $json.task_type }}",
              "rightValue": "check_conflicts",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combineOperation": "any"
        },
        "options": {}
      },
      "type": "n8n-nodes-base.switch",
      "typeVersion": 3,
      "position": [-900, 300],
      "id": "task-type-router",
      "name": "Task Type Router"
    },
    {
      "parameters": {
        "operation": "list",
        "calendarId": {
          "__rl": true,
          "value": "primary",
          "mode": "list",
          "cachedResultName": "Primary"
        },
        "options": {
          "timeMin": "={{ $now.toISO() }}",
          "timeMax": "={{ $now.plus({days: 30}).toISO() }}",
          "singleEvents": true,
          "orderBy": "startTime"
        }
      },
      "type": "n8n-nodes-base.googleCalendar",
      "typeVersion": 2,
      "position": [-600, 100],
      "id": "get-calendar-events",
      "name": "Get Calendar Events"
    },
    {
      "parameters": {
        "promptType": "define",
        "text": "=Analyze calendar data and task requirements:\n\nTask: {{ $('Calendar Agent Input').item.json.task_data }}\nExisting Events: {{ JSON.stringify($json) }}\nUser Preferences: {{ $('Calendar Agent Input').item.json.context }}\n\nProvide smart scheduling recommendations.",
        "options": {
          "systemMessage": "Du bist der Calendar Agent für das Jarvis System. Deine Aufgaben:\n\n**Smart Scheduling:**\n- Analysiere bestehende Termine und finde optimale Zeitslots\n- Berücksichtige Reisezeiten zwischen Terminen\n- Respektiere Focus-Time und Arbeitszeiten\n- Erkenne und löse Terminkonlikte\n\n**Conflict Management:**\n- Identifiziere überlappende Termine\n- Schlage alternative Zeiten vor\n- Priorisiere basierend auf Wichtigkeit\n\n**Meeting Coordination:**\n- Koordiniere mit mehreren Teilnehmern\n- Berücksichtige verschiedene Zeitzonen\n- Plane Pufferzeiten ein\n\n**Output Format:**\n{\n  \"action\": \"schedule|reschedule|block|conflict_detected\",\n  \"recommended_time\": \"ISO datetime\",\n  \"duration_minutes\": number,\n  \"conflicts\": [],\n  \"travel_time_needed\": \"Xmin\",\n  \"participants\": [],\n  \"location\": \"string\",\n  \"buffer_time\": \"Xmin\",\n  \"alternative_times\": [],\n  \"reasoning\": \"explanation\"\n}"
        }
      },
      "type": "@n8n/n8n-nodes-langchain.agent",
      "typeVersion": 1.7,
      "position": [-300, 200],
      "id": "calendar-ai-analyzer",
      "name": "Calendar AI Analyzer"
    },
    {
      "parameters": {
        "operation": "create",
        "calendarId": {
          "__rl": true,
          "value": "primary",
          "mode": "list",
          "cachedResultName": "Primary"
        },
        "start": "={{ $json.recommended_time }}",
        "end": "={{ $now.fromISO($json.recommended_time).plus({minutes: $json.duration_minutes}).toISO() }}",
        "summary": "={{ $('Calendar Agent Input').item.json.task_data.title }}",
        "description": "={{ $('Calendar Agent Input').item.json.task_data.description }}",
        "location": "={{ $json.location }}",
        "options": {
          "sendNotifications": true
        }
      },
      "type": "n8n-nodes-base.googleCalendar",
      "typeVersion": 2,
      "position": [0, 200],
      "id": "create-calendar-event",
      "name": "Create Calendar Event"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO calendar_actions (user_id, action_type, event_data, ai_reasoning, timestamp) VALUES ($1, $2, $3, $4, NOW())",
        "additionalFields": {
          "queryParameters": "={{ [$('Calendar Agent Input').item.json.user_id, $json.action, JSON.stringify($json), $json.reasoning] }}"
        }
      },
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.5,
      "position": [300, 200],
      "id": "log-calendar-action",
      "name": "Log Calendar Action"
    },
    {
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "success",
              "name": "success",
              "value": true,
              "type": "boolean"
            },
            {
              "id": "action_taken",
              "name": "action_taken",
              "value": "={{ $('Calendar AI Analyzer').item.json.action }}",
              "type": "string"
            },
            {
              "id": "event_id",
              "name": "event_id",
              "value": "={{ $('Create Calendar Event').item.json.id }}",
              "type": "string"
            },
            {
              "id": "scheduled_time",
              "name": "scheduled_time",
              "value": "={{ $('Calendar AI Analyzer').item.json.recommended_time }}",
              "type": "string"
            },
            {
              "id": "conflicts_resolved",
              "name": "conflicts_resolved",
              "value": "={{ $('Calendar AI Analyzer').item.json.conflicts.length }}",
              "type": "number"
            },
            {
              "id": "reasoning",
              "name": "reasoning",
              "value": "={{ $('Calendar AI Analyzer').item.json.reasoning }}",
              "type": "string"
            }
          ]
        },
        "options": {}
      },
      "type": "n8n-nodes-base.set",
      "typeVersion": 3.4,
      "position": [600, 200],
      "id": "format-response",
      "name": "Format Response"
    }
  ],
  "connections": {
    "Calendar Agent Input": {
      "main": [
        [
          {
            "node": "Task Type Router",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Task Type Router": {
      "main": [
        [
          {
            "node": "Get Calendar Events",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Get Calendar Events",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Get Calendar Events",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Get Calendar Events",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Get Calendar Events",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Calendar Events": {
      "main": [
        [
          {
            "node": "Calendar AI Analyzer",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Calendar AI Analyzer": {
      "main": [
        [
          {
            "node": "Create Calendar Event",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Create Calendar Event": {
      "main": [
        [
          {
            "node": "Log Calendar Action",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Log Calendar Action": {
      "main": [
        [
          {
            "node": "Format Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [
    {
      "createdAt": "2024-12-29T10:00:00.000Z",
      "updatedAt": "2024-12-29T10:00:00.000Z",
      "id": "calendar-agent",
      "name": "calendar-agent"
    }
  ],
  "triggerCount": 0,
  "updatedAt": "2024-12-29T10:00:00.000Z",
  "versionId": "1"
}

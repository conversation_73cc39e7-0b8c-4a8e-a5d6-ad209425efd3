{"name": "Call Agent - Comprehensive Call Management", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "task_type", "type": "string"}, {"name": "task_data", "type": "object"}, {"name": "user_id", "type": "string"}, {"name": "priority", "type": "string"}, {"name": "context", "type": "object"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1200, 300], "id": "call-agent-input", "name": "Call Agent Input"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "make_call", "leftValue": "={{ $json.task_type }}", "rightValue": "make_call", "operator": {"type": "string", "operation": "equals"}}, {"id": "schedule_callback", "leftValue": "={{ $json.task_type }}", "rightValue": "schedule_callback", "operator": {"type": "string", "operation": "equals"}}, {"id": "process_voicemail", "leftValue": "={{ $json.task_type }}", "rightValue": "process_voicemail", "operator": {"type": "string", "operation": "equals"}}, {"id": "employee_lookup", "leftValue": "={{ $json.task_type }}", "rightValue": "employee_lookup", "operator": {"type": "string", "operation": "equals"}}, {"id": "call_routing", "leftValue": "={{ $json.task_type }}", "rightValue": "call_routing", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "any"}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-900, 300], "id": "call-task-router", "name": "Call Task Router"}, {"parameters": {"promptType": "define", "text": "=Analyze call requirements and prepare call strategy:\n\nCall Data: {{ $('Call Agent Input').item.json.task_data }}\nContact Info: {{ $('Call Agent Input').item.json.task_data.contact }}\nCall Purpose: {{ $('Call Agent Input').item.json.task_data.purpose }}\nContext: {{ $('Call Agent Input').item.json.context }}\n\nDetermine optimal call approach and script.", "options": {"systemMessage": "Du bist der Call Agent für das Jarvis System. Deine Aufgaben:\n\n**Intelligent Call Management:**\n- Analy<PERSON><PERSON> und wähle passende Strategie\n- Bereite kontextuelle Gesprächsführung vor\n- Erkenne optimale Anrufzeiten\n- Handhabe Multi-Language Support\n\n**Employee Lookup & Routing:**\n- Finde passende Ansprechpartner\n- Berücksichtige Verfügbarkeit und Expertise\n- Route Anrufe intelligent weiter\n- Handhabe Eskalationen\n\n**Voicemail Processing:**\n- Transkribiere Sprachnachrichten\n- Extrahiere wichtige Informationen\n- Kategorisiere nach Dringlichkeit\n- Plane Follow-up Aktionen\n\n**Callback Scheduling:**\n- Finde optimale Rückrufzeiten\n- Berücksichtige Zeitzonen\n- Koordiniere mit Kalendern\n- Sende Erinnerungen\n\n**Output Format:**\n{\n  \"call_strategy\": \"cold_call|warm_call|follow_up|support\",\n  \"optimal_time\": \"ISO datetime\",\n  \"script_template\": \"string\",\n  \"expected_duration\": \"Xmin\",\n  \"language\": \"de|en|fr|es\",\n  \"priority_level\": \"critical|high|medium|low\",\n  \"routing_target\": \"string\",\n  \"fallback_options\": [],\n  \"success_criteria\": [],\n  \"follow_up_required\": boolean\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-600, 200], "id": "call-ai-strategist", "name": "Call AI Strategist"}, {"parameters": {"workflowId": "{{ $('Call Agent Input').item.json.task_data.vapi_workflow_id || 'general_caller' }}", "workflowInputs": {"phone_number": "={{ $('Call Agent Input').item.json.task_data.phone_number }}", "call_purpose": "={{ $('Call AI Strategist').item.json.call_strategy }}", "script": "={{ $('Call AI Strategist').item.json.script_template }}", "language": "={{ $('Call AI Strategist').item.json.language }}", "context": "={{ JSON.stringify($('Call Agent Input').item.json.context) }}"}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.1, "position": [-300, 100], "id": "execute-vapi-call", "name": "Execute VAPI Call"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO call_log (user_id, phone_number, call_purpose, call_status, duration, transcript, ai_analysis, timestamp) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())", "additionalFields": {"queryParameters": "={{ [$('Call Agent Input').item.json.user_id, $('Call Agent Input').item.json.task_data.phone_number, $('Call AI Strategist').item.json.call_strategy, $('Execute VAPI Call').item.json.status, $('Execute VAPI Call').item.json.duration, $('Execute VAPI Call').item.json.transcript, JSON.stringify($('Call AI Strategist').item.json)] }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [0, 100], "id": "log-call-details", "name": "Log Call Details"}, {"parameters": {"operation": "create", "calendarId": {"__rl": true, "value": "primary", "mode": "list", "cachedResultName": "Primary"}, "start": "={{ $('Call AI Strategist').item.json.optimal_time }}", "end": "={{ $now.fromISO($('Call AI Strategist').item.json.optimal_time).plus({minutes: 15}).toISO() }}", "summary": "=Follow-up Call: {{ $('Call Agent Input').item.json.task_data.contact.name }}", "description": "=Scheduled follow-up based on call outcome. Purpose: {{ $('Call AI Strategist').item.json.call_strategy }}", "options": {"sendNotifications": true}}, "type": "n8n-nodes-base.googleCalendar", "typeVersion": 2, "position": [0, 300], "id": "schedule-follow-up", "name": "Schedule Follow-up"}, {"parameters": {"assignments": {"assignments": [{"id": "success", "name": "success", "value": true, "type": "boolean"}, {"id": "call_completed", "name": "call_completed", "value": "={{ $('Execute VAPI Call').item.json.status === 'completed' }}", "type": "boolean"}, {"id": "call_duration", "name": "call_duration", "value": "={{ $('Execute VAPI Call').item.json.duration }}", "type": "string"}, {"id": "call_transcript", "name": "call_transcript", "value": "={{ $('Execute VAPI Call').item.json.transcript }}", "type": "string"}, {"id": "follow_up_scheduled", "name": "follow_up_scheduled", "value": "={{ $('Call AI Strategist').item.json.follow_up_required }}", "type": "boolean"}, {"id": "call_strategy_used", "name": "call_strategy_used", "value": "={{ $('Call AI Strategist').item.json.call_strategy }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [300, 200], "id": "format-call-response", "name": "Format Call Response"}], "connections": {"Call Agent Input": {"main": [[{"node": "Call Task Router", "type": "main", "index": 0}]]}, "Call Task Router": {"main": [[{"node": "Call AI Strategist", "type": "main", "index": 0}], [{"node": "Call AI Strategist", "type": "main", "index": 0}], [{"node": "Call AI Strategist", "type": "main", "index": 0}], [{"node": "Call AI Strategist", "type": "main", "index": 0}], [{"node": "Call AI Strategist", "type": "main", "index": 0}]]}, "Call AI Strategist": {"main": [[{"node": "Execute VAPI Call", "type": "main", "index": 0}]]}, "Execute VAPI Call": {"main": [[{"node": "Log Call Details", "type": "main", "index": 0}, {"node": "Schedule Follow-up", "type": "main", "index": 0}]]}, "Log Call Details": {"main": [[{"node": "Format Call Response", "type": "main", "index": 0}]]}, "Schedule Follow-up": {"main": [[{"node": "Format Call Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-12-29T10:00:00.000Z", "updatedAt": "2024-12-29T10:00:00.000Z", "id": "call-agent", "name": "call-agent"}], "triggerCount": 0, "updatedAt": "2024-12-29T10:00:00.000Z", "versionId": "1"}
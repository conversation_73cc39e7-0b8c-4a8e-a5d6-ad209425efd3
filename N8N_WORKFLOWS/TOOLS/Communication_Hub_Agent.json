{"name": "Communication Hub Agent - Multi-Channel Messaging", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "task_type", "type": "string"}, {"name": "task_data", "type": "object"}, {"name": "user_id", "type": "string"}, {"name": "priority", "type": "string"}, {"name": "context", "type": "object"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1200, 300], "id": "communication-agent-input", "name": "Communication Agent Input"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "send_telegram", "leftValue": "={{ $json.task_type }}", "rightValue": "send_telegram", "operator": {"type": "string", "operation": "equals"}}, {"id": "send_whatsapp", "leftValue": "={{ $json.task_type }}", "rightValue": "send_whatsapp", "operator": {"type": "string", "operation": "equals"}}, {"id": "send_slack", "leftValue": "={{ $json.task_type }}", "rightValue": "send_slack", "operator": {"type": "string", "operation": "equals"}}, {"id": "send_sms", "leftValue": "={{ $json.task_type }}", "rightValue": "send_sms", "operator": {"type": "string", "operation": "equals"}}, {"id": "broadcast_message", "leftValue": "={{ $json.task_type }}", "rightValue": "broadcast_message", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "any"}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-900, 300], "id": "communication-router", "name": "Communication Router"}, {"parameters": {"promptType": "define", "text": "=Analyze communication requirements and optimize message delivery:\n\nMessage Data: {{ $('Communication Agent Input').item.json.task_data }}\nRecipients: {{ $('Communication Agent Input').item.json.task_data.recipients }}\nPriority: {{ $('Communication Agent Input').item.json.priority }}\nContext: {{ $('Communication Agent Input').item.json.context }}\n\nDetermine optimal communication strategy.", "options": {"systemMessage": "Du bist der Communication Hub Agent für das Jarvis System. Deine Aufgaben:\n\n**Smart Channel Selection:**\n- Wähle optimalen Kommunikationskanal basierend auf:\n  - Dringlichkeit der Nachricht\n  - Empfänger-Präferenzen\n  - Verfügbarkeit der Kanäle\n  - Nachrichtentyp und -inhalt\n\n**Message Optimization:**\n- Passe Nachrichtenformat an Kanal an\n- Berücksichtige Zeichenlimits\n- Optimiere für mobile/desktop Anzeige\n- Verwende passende Emojis und Formatierung\n\n**Delivery Management:**\n- Plane optimale Sendezeiten\n- Handhabe Rate Limits\n- Implementiere Fallback-Mechanismen\n- Tracke Delivery Status\n\n**Template Management:**\n- Wähle passende Message Templates\n- Personalisiere Nachrichten\n- Berücksichtige Sprach-Präferenzen\n\n**Output Format:**\n{\n  \"optimal_channel\": \"telegram|whatsapp|slack|sms|email\",\n  \"fallback_channels\": [],\n  \"optimized_message\": \"string\",\n  \"send_time\": \"ISO datetime\",\n  \"personalization_applied\": boolean,\n  \"template_used\": \"string\",\n  \"delivery_priority\": \"immediate|scheduled|batch\",\n  \"rate_limit_consideration\": boolean,\n  \"estimated_delivery_time\": \"string\"\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-600, 200], "id": "communication-ai-optimizer", "name": "Communication AI Optimizer"}, {"parameters": {"chatId": "={{ $('Communication Agent Input').item.json.task_data.telegram_chat_id }}", "text": "={{ $('Communication AI Optimizer').item.json.optimized_message }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-300, 0], "id": "send-telegram-message", "name": "Send Telegram Message"}, {"parameters": {"channel": "={{ $('Communication Agent Input').item.json.task_data.slack_channel }}", "text": "={{ $('Communication AI Optimizer').item.json.optimized_message }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.2, "position": [-300, 200], "id": "send-slack-message", "name": "Send Slack Message"}, {"parameters": {"method": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilioApi.accountSid }}/Messages.json", "authentication": "predefinedCredentialType", "nodeCredentialType": "t<PERSON><PERSON><PERSON><PERSON>", "sendBody": true, "bodyParameters": {"parameters": [{"name": "To", "value": "={{ $('Communication Agent Input').item.json.task_data.phone_number }}"}, {"name": "From", "value": "={{ $credentials.twilioApi.fromPhoneNumber }}"}, {"name": "Body", "value": "={{ $('Communication AI Optimizer').item.json.optimized_message }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-300, 400], "id": "send-sms-message", "name": "Send SMS Message"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO communication_log (user_id, channel, recipient, message, delivery_status, timestamp) VALUES ($1, $2, $3, $4, $5, NOW())", "additionalFields": {"queryParameters": "={{ [$('Communication Agent Input').item.json.user_id, $('Communication AI Optimizer').item.json.optimal_channel, JSON.stringify($('Communication Agent Input').item.json.task_data.recipients), $('Communication AI Optimizer').item.json.optimized_message, 'sent'] }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [0, 200], "id": "log-communication", "name": "Log Communication"}, {"parameters": {"assignments": {"assignments": [{"id": "success", "name": "success", "value": true, "type": "boolean"}, {"id": "channel_used", "name": "channel_used", "value": "={{ $('Communication AI Optimizer').item.json.optimal_channel }}", "type": "string"}, {"id": "message_sent", "name": "message_sent", "value": "={{ $('Communication AI Optimizer').item.json.optimized_message }}", "type": "string"}, {"id": "delivery_time", "name": "delivery_time", "value": "={{ $('Communication AI Optimizer').item.json.estimated_delivery_time }}", "type": "string"}, {"id": "personalization_applied", "name": "personalization_applied", "value": "={{ $('Communication AI Optimizer').item.json.personalization_applied }}", "type": "boolean"}, {"id": "template_used", "name": "template_used", "value": "={{ $('Communication AI Optimizer').item.json.template_used }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [300, 200], "id": "format-communication-response", "name": "Format Communication Response"}], "connections": {"Communication Agent Input": {"main": [[{"node": "Communication Router", "type": "main", "index": 0}]]}, "Communication Router": {"main": [[{"node": "Communication AI Optimizer", "type": "main", "index": 0}], [{"node": "Communication AI Optimizer", "type": "main", "index": 0}], [{"node": "Communication AI Optimizer", "type": "main", "index": 0}], [{"node": "Communication AI Optimizer", "type": "main", "index": 0}], [{"node": "Communication AI Optimizer", "type": "main", "index": 0}]]}, "Communication AI Optimizer": {"main": [[{"node": "Send Telegram Message", "type": "main", "index": 0}, {"node": "Send Slack Message", "type": "main", "index": 0}, {"node": "Send SMS Message", "type": "main", "index": 0}]]}, "Send Telegram Message": {"main": [[{"node": "Log Communication", "type": "main", "index": 0}]]}, "Send Slack Message": {"main": [[{"node": "Log Communication", "type": "main", "index": 0}]]}, "Send SMS Message": {"main": [[{"node": "Log Communication", "type": "main", "index": 0}]]}, "Log Communication": {"main": [[{"node": "Format Communication Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-12-29T10:00:00.000Z", "updatedAt": "2024-12-29T10:00:00.000Z", "id": "communication-hub-agent", "name": "communication-hub-agent"}], "triggerCount": 0, "updatedAt": "2024-12-29T10:00:00.000Z", "versionId": "1"}
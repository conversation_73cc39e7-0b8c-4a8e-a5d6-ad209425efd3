{"name": "Document Processor - Multi-Format Document Analysis", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "task_type", "type": "string"}, {"name": "task_data", "type": "object"}, {"name": "user_id", "type": "string"}, {"name": "priority", "type": "string"}, {"name": "context", "type": "object"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1200, 300], "id": "document-processor-input", "name": "Document Processor Input"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "pdf_extraction", "leftValue": "={{ $json.task_type }}", "rightValue": "pdf_extraction", "operator": {"type": "string", "operation": "equals"}}, {"id": "ocr_processing", "leftValue": "={{ $json.task_type }}", "rightValue": "ocr_processing", "operator": {"type": "string", "operation": "equals"}}, {"id": "structure_analysis", "leftValue": "={{ $json.task_type }}", "rightValue": "structure_analysis", "operator": {"type": "string", "operation": "equals"}}, {"id": "table_extraction", "leftValue": "={{ $json.task_type }}", "rightValue": "table_extraction", "operator": {"type": "string", "operation": "equals"}}, {"id": "language_detection", "leftValue": "={{ $json.task_type }}", "rightValue": "language_detection", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "any"}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-900, 300], "id": "document-task-router", "name": "Document Task Router"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/chat/completions", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-4-vision-preview"}, {"name": "messages", "value": "=[{\"role\": \"user\", \"content\": [{\"type\": \"text\", \"text\": \"Extract and analyze all text from this document. Identify structure, tables, and key information.\"}, {\"type\": \"image_url\", \"image_url\": {\"url\": $('Document Processor Input').item.json.task_data.document_url}}]}]"}, {"name": "max_tokens", "value": "4000"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-600, 100], "id": "gpt-vision-ocr", "name": "GPT Vision OCR"}, {"parameters": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "anthropicApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "claude-3-sonnet-20240229"}, {"name": "max_tokens", "value": "4000"}, {"name": "messages", "value": "=[{\"role\": \"user\", \"content\": [{\"type\": \"image\", \"source\": {\"type\": \"base64\", \"media_type\": \"image/jpeg\", \"data\": $('Document Processor Input').item.json.task_data.document_base64}}, {\"type\": \"text\", \"text\": \"Analyze this document and extract structured information including tables, headings, and key data points.\"}]}]"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-600, 300], "id": "claude-vision-analysis", "name": "Claude Vision Analysis"}, {"parameters": {"promptType": "define", "text": "=Analyze and structure document content:\n\nGPT Vision Results: {{ JSON.stringify($('GPT Vision OCR').item.json) }}\nClaude Analysis: {{ JSON.stringify($('Claude Vision Analysis').item.json) }}\nDocument Info: {{ $('Document Processor Input').item.json.task_data }}\nProcessing Requirements: {{ $('Document Processor Input').item.json.context }}\n\nProvide comprehensive document analysis.", "options": {"systemMessage": "Du bist der Document Processor für das Jarvis System. Deine Aufgaben:\n\n**PDF Text Extraction:**\n- Extrahiere Text aus PDFs mit hoher Genauigkeit\n- Handhabe verschiedene PDF-Formate und -Qualitäten\n- Erkenne und korrigiere OCR-Fehler\n- Bewahre Dokumentstruktur und Formatierung\n\n**Structure Analysis:**\n- Identifiziere Überschriften, Absätze, Listen\n- Erkenne Dokumenthierarchie und -gliederung\n- Analysiere Layout und Formatierung\n- Extrahiere Metadaten und Eigenschaften\n\n**Table Extraction:**\n- Erkenne und extrahiere Tabellen\n- Strukturiere Tabellendaten in JSON/CSV\n- Handhabe komplexe Tabellenformate\n- Validiere Datenintegrität\n\n**Multi-Format Support:**\n- Verarbeite PDF, DOCX, Images, TXT\n- Handhabe verschiedene Encodings\n- Konvertiere zwischen Formaten\n- Optimiere für verschiedene Dateitypen\n\n**Language Detection:**\n- Erkenne Dokumentsprache automatisch\n- Handhabe mehrsprachige Dokumente\n- Optimiere Verarbeitung pro Sprache\n- Berücksichtige regionale Besonderheiten\n\n**Output Format:**\n{\n  \"extraction_successful\": boolean,\n  \"detected_language\": \"string\",\n  \"document_type\": \"pdf|docx|image|txt\",\n  \"page_count\": number,\n  \"extracted_text\": \"string\",\n  \"structured_content\": {\n    \"headings\": [],\n    \"paragraphs\": [],\n    \"tables\": [],\n    \"lists\": [],\n    \"metadata\": {}\n  },\n  \"confidence_score\": number,\n  \"processing_time\": \"string\",\n  \"errors_found\": [],\n  \"quality_assessment\": \"high|medium|low\"\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-300, 200], "id": "document-ai-analyzer", "name": "Document AI Analyzer"}, {"parameters": {"operation": "upsert", "index": "jarvis-documents-knowledge", "vectors": {"values": [{"id": "={{ $('Document Processor Input').item.json.user_id }}-{{ $now.toUnixInteger() }}", "values": "={{ $json.embedding_vector }}", "metadata": {"document_name": "={{ $('Document Processor Input').item.json.task_data.document_name }}", "extracted_text": "={{ $json.extracted_text }}", "document_type": "={{ $json.document_type }}", "language": "={{ $json.detected_language }}", "confidence": "={{ $json.confidence_score }}", "timestamp": "={{ $now.toISO() }}", "user_id": "={{ $('Document Processor Input').item.json.user_id }}"}}]}}, "type": "n8n-nodes-base.pinecone", "typeVersion": 1, "position": [0, 100], "id": "store-document-knowledge", "name": "Store Document Knowledge"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO processed_documents (user_id, document_name, document_type, extracted_text, structured_content, language, confidence_score, processing_time, timestamp) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())", "additionalFields": {"queryParameters": "={{ [$('Document Processor Input').item.json.user_id, $('Document Processor Input').item.json.task_data.document_name, $json.document_type, $json.extracted_text, JSON.stringify($json.structured_content), $json.detected_language, $json.confidence_score, $json.processing_time] }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [0, 300], "id": "log-document-processing", "name": "Log Document Processing"}, {"parameters": {"operation": "create", "resource": "file", "name": "={{ $('Document Processor Input').item.json.task_data.document_name + '_processed.json' }}", "parents": {"__rl": true, "value": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms", "mode": "list", "cachedResultName": "Processed Documents"}, "options": {"uploadType": "media"}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [0, 500], "id": "save-processed-document", "name": "Save Processed Document"}, {"parameters": {"assignments": {"assignments": [{"id": "success", "name": "success", "value": true, "type": "boolean"}, {"id": "extraction_successful", "name": "extraction_successful", "value": "={{ $('Document AI Analyzer').item.json.extraction_successful }}", "type": "boolean"}, {"id": "document_type", "name": "document_type", "value": "={{ $('Document AI Analyzer').item.json.document_type }}", "type": "string"}, {"id": "detected_language", "name": "detected_language", "value": "={{ $('Document AI Analyzer').item.json.detected_language }}", "type": "string"}, {"id": "page_count", "name": "page_count", "value": "={{ $('Document AI Analyzer').item.json.page_count }}", "type": "number"}, {"id": "extracted_text", "name": "extracted_text", "value": "={{ $('Document AI Analyzer').item.json.extracted_text }}", "type": "string"}, {"id": "structured_content", "name": "structured_content", "value": "={{ $('Document AI Analyzer').item.json.structured_content }}", "type": "object"}, {"id": "confidence_score", "name": "confidence_score", "value": "={{ $('Document AI Analyzer').item.json.confidence_score }}", "type": "number"}, {"id": "quality_assessment", "name": "quality_assessment", "value": "={{ $('Document AI Analyzer').item.json.quality_assessment }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [300, 200], "id": "format-document-response", "name": "Format Document Response"}], "connections": {"Document Processor Input": {"main": [[{"node": "Document Task Router", "type": "main", "index": 0}]]}, "Document Task Router": {"main": [[{"node": "GPT Vision OCR", "type": "main", "index": 0}, {"node": "Claude Vision Analysis", "type": "main", "index": 0}], [{"node": "GPT Vision OCR", "type": "main", "index": 0}, {"node": "Claude Vision Analysis", "type": "main", "index": 0}], [{"node": "GPT Vision OCR", "type": "main", "index": 0}, {"node": "Claude Vision Analysis", "type": "main", "index": 0}], [{"node": "GPT Vision OCR", "type": "main", "index": 0}, {"node": "Claude Vision Analysis", "type": "main", "index": 0}], [{"node": "GPT Vision OCR", "type": "main", "index": 0}, {"node": "Claude Vision Analysis", "type": "main", "index": 0}]]}, "GPT Vision OCR": {"main": [[{"node": "Document AI Analyzer", "type": "main", "index": 0}]]}, "Claude Vision Analysis": {"main": [[{"node": "Document AI Analyzer", "type": "main", "index": 0}]]}, "Document AI Analyzer": {"main": [[{"node": "Store Document Knowledge", "type": "main", "index": 0}, {"node": "Log Document Processing", "type": "main", "index": 0}, {"node": "Save Processed Document", "type": "main", "index": 0}]]}, "Store Document Knowledge": {"main": [[{"node": "Format Document Response", "type": "main", "index": 0}]]}, "Log Document Processing": {"main": [[{"node": "Format Document Response", "type": "main", "index": 0}]]}, "Save Processed Document": {"main": [[{"node": "Format Document Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-12-29T10:00:00.000Z", "updatedAt": "2024-12-29T10:00:00.000Z", "id": "document-processor", "name": "document-processor"}], "triggerCount": 0, "updatedAt": "2024-12-29T10:00:00.000Z", "versionId": "1"}
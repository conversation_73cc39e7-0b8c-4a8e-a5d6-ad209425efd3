{"name": "Email Agent - Intelligent Email Management", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "task_type", "type": "string"}, {"name": "task_data", "type": "object"}, {"name": "user_id", "type": "string"}, {"name": "priority", "type": "string"}, {"name": "context", "type": "object"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1200, 300], "id": "email-agent-input", "name": "Email Agent Input"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "classify_emails", "leftValue": "={{ $json.task_type }}", "rightValue": "classify_emails", "operator": {"type": "string", "operation": "equals"}}, {"id": "compose_email", "leftValue": "={{ $json.task_type }}", "rightValue": "compose_email", "operator": {"type": "string", "operation": "equals"}}, {"id": "auto_reply", "leftValue": "={{ $json.task_type }}", "rightValue": "auto_reply", "operator": {"type": "string", "operation": "equals"}}, {"id": "priority_inbox", "leftValue": "={{ $json.task_type }}", "rightValue": "priority_inbox", "operator": {"type": "string", "operation": "equals"}}, {"id": "schedule_followup", "leftValue": "={{ $json.task_type }}", "rightValue": "schedule_followup", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "any"}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-900, 300], "id": "email-task-router", "name": "Email Task Router"}, {"parameters": {"operation": "getAll", "returnAll": false, "limit": 50, "options": {"q": "is:unread", "labelIds": ["INBOX"]}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-600, 100], "id": "get-unread-emails", "name": "Get Unread Emails"}, {"parameters": {"promptType": "define", "text": "=Analyze email content and classify:\n\nEmail Data: {{ JSON.stringify($json) }}\nTask: {{ $('Email Agent Input').item.json.task_data }}\nUser Context: {{ $('Email Agent Input').item.json.context }}\n\nProvide intelligent email management recommendations.", "options": {"systemMessage": "Du bist der Email Agent für das Jarvis System. Deine Aufgaben:\n\n**Email Classification:**\n- Kategorisiere E-Mails nach Wichtigkeit (critical/high/medium/low)\n- Erkenne Spam, Promotions, Updates, persönliche Nachrichten\n- Identifiziere Meeting-Einladungen und Terminanfragen\n- Erkenne Follow-up benötigende E-Mails\n\n**Auto-Reply Management:**\n- Generiere kontextuelle automatische Antworten\n- Verwende passende Templates basierend auf E-Mail-Typ\n- Berücksichtige Absender-Beziehung und Dringlichkeit\n\n**Priority Inbox:**\n- Sortiere E-Mails nach Wichtigkeit und Dringlichkeit\n- Erkenne VIP-Absender und wichtige Keywords\n- Schlage Aktionen vor (antworten, weiterleiten, archivieren)\n\n**Output Format:**\n{\n  \"classification\": \"critical|high|medium|low|spam\",\n  \"category\": \"meeting|personal|business|promotion|update\",\n  \"action_required\": \"reply|forward|schedule|archive|flag\",\n  \"priority_score\": number,\n  \"suggested_response\": \"string\",\n  \"follow_up_needed\": boolean,\n  \"follow_up_date\": \"ISO datetime\",\n  \"key_points\": [],\n  \"sender_importance\": \"vip|regular|unknown\",\n  \"reasoning\": \"explanation\"\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-300, 200], "id": "email-ai-classifier", "name": "Email AI Classifier"}, {"parameters": {"operation": "send", "to": "={{ $('Get Unread Emails').item.json.payload.headers.find(h => h.name === 'From').value }}", "subject": "=Re: {{ $('Get Unread Emails').item.json.payload.headers.find(h => h.name === 'Subject').value }}", "message": "={{ $json.suggested_response }}", "options": {"replyTo": "={{ $('Get Unread Emails').item.json.id }}"}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [0, 200], "id": "send-email-reply", "name": "Send Email Reply"}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Get Unread Emails').item.json.id }}", "labelIds": ["={{ $('Email AI Classifier').item.json.classification === 'critical' ? 'IMPORTANT' : 'CATEGORY_UPDATES' }}"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [300, 100], "id": "apply-email-labels", "name": "Apply Email Labels"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO email_actions (user_id, email_id, classification, action_taken, ai_reasoning, timestamp) VALUES ($1, $2, $3, $4, $5, NOW())", "additionalFields": {"queryParameters": "={{ [$('Email Agent Input').item.json.user_id, $('Get Unread Emails').item.json.id, $json.classification, $json.action_required, $json.reasoning] }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [300, 300], "id": "log-email-action", "name": "Log Email Action"}, {"parameters": {"assignments": {"assignments": [{"id": "success", "name": "success", "value": true, "type": "boolean"}, {"id": "emails_processed", "name": "emails_processed", "value": "={{ $('Get Unread Emails').all().length }}", "type": "number"}, {"id": "classification_results", "name": "classification_results", "value": "={{ $('Email AI Classifier').all() }}", "type": "object"}, {"id": "actions_taken", "name": "actions_taken", "value": "={{ $('Email AI Classifier').all().map(item => item.json.action_required) }}", "type": "object"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [600, 200], "id": "format-email-response", "name": "Format Email Response"}], "connections": {"Email Agent Input": {"main": [[{"node": "Email Task Router", "type": "main", "index": 0}]]}, "Email Task Router": {"main": [[{"node": "Get Unread Emails", "type": "main", "index": 0}], [{"node": "Get Unread Emails", "type": "main", "index": 0}], [{"node": "Get Unread Emails", "type": "main", "index": 0}], [{"node": "Get Unread Emails", "type": "main", "index": 0}], [{"node": "Get Unread Emails", "type": "main", "index": 0}]]}, "Get Unread Emails": {"main": [[{"node": "Email AI Classifier", "type": "main", "index": 0}]]}, "Email AI Classifier": {"main": [[{"node": "Send Email Reply", "type": "main", "index": 0}, {"node": "Apply Email Labels", "type": "main", "index": 0}, {"node": "Log Email Action", "type": "main", "index": 0}]]}, "Send Email Reply": {"main": [[{"node": "Format Email Response", "type": "main", "index": 0}]]}, "Apply Email Labels": {"main": [[{"node": "Format Email Response", "type": "main", "index": 0}]]}, "Log Email Action": {"main": [[{"node": "Format Email Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-12-29T10:00:00.000Z", "updatedAt": "2024-12-29T10:00:00.000Z", "id": "email-agent", "name": "email-agent"}], "triggerCount": 0, "updatedAt": "2024-12-29T10:00:00.000Z", "versionId": "1"}
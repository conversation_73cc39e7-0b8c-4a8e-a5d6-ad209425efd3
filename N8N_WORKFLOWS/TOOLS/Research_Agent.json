{"name": "Research Agent - Intelligent Research & Analysis", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "task_type", "type": "string"}, {"name": "task_data", "type": "object"}, {"name": "user_id", "type": "string"}, {"name": "priority", "type": "string"}, {"name": "context", "type": "object"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1200, 300], "id": "research-agent-input", "name": "Research Agent Input"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "web_research", "leftValue": "={{ $json.task_type }}", "rightValue": "web_research", "operator": {"type": "string", "operation": "equals"}}, {"id": "document_analysis", "leftValue": "={{ $json.task_type }}", "rightValue": "document_analysis", "operator": {"type": "string", "operation": "equals"}}, {"id": "fact_checking", "leftValue": "={{ $json.task_type }}", "rightValue": "fact_checking", "operator": {"type": "string", "operation": "equals"}}, {"id": "trend_analysis", "leftValue": "={{ $json.task_type }}", "rightValue": "trend_analysis", "operator": {"type": "string", "operation": "equals"}}, {"id": "comprehensive_report", "leftValue": "={{ $json.task_type }}", "rightValue": "comprehensive_report", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "any"}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-900, 300], "id": "research-task-router", "name": "Research Task Router"}, {"parameters": {"method": "POST", "url": "https://api.perplexity.ai/chat/completions", "authentication": "predefinedCredentialType", "nodeCredentialType": "perplexityApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "llama-3.1-sonar-large-128k-online"}, {"name": "messages", "value": "=[{\"role\": \"user\", \"content\": $('Research Agent Input').item.json.task_data.query}]"}, {"name": "max_tokens", "value": "2000"}, {"name": "temperature", "value": "0.2"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-600, 100], "id": "perplexity-research", "name": "Perplexity Research"}, {"parameters": {"method": "POST", "url": "https://api.tavily.com/search", "authentication": "predefinedCredentialType", "nodeCredentialType": "tavily<PERSON><PERSON>", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "query", "value": "={{ $('Research Agent Input').item.json.task_data.query }}"}, {"name": "search_depth", "value": "advanced"}, {"name": "include_answer", "value": true}, {"name": "include_raw_content", "value": true}, {"name": "max_results", "value": "10"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-600, 300], "id": "tavily-search", "name": "<PERSON><PERSON>"}, {"parameters": {"promptType": "define", "text": "=Analyze research data and create comprehensive report:\n\nPerplexity Results: {{ JSON.stringify($('Perplexity Research').item.json) }}\nTavily Results: {{ JSON.stringify($('Tavily Search').item.json) }}\nResearch Query: {{ $('Research Agent Input').item.json.task_data.query }}\nContext: {{ $('Research Agent Input').item.json.context }}\n\nProvide detailed analysis and insights.", "options": {"systemMessage": "Du bist der Research Agent für das Jarvis System. Deine Aufgaben:\n\n**Multi-Source Research:**\n- Kombiniere Ergebnisse von Perplexity und Tavily\n- Identifiziere übereinstimmende und widersprüchliche Informationen\n- Bewerte Quellen-Glaubwürdigkeit und Aktualität\n\n**Document Analysis:**\n- Extrahiere Schlüsselinformationen aus verschiedenen Quellen\n- Erkenne Patterns und Trends in den Daten\n- Strukturiere Informationen logisch\n\n**Fact-Checking:**\n- Verifiziere Informationen gegen mehrere Quellen\n- Markiere unsichere oder widersprüchliche Aussagen\n- Bewerte Informations-Zuverlässigkeit\n\n**Comprehensive Reporting:**\n- Erstelle Executive Summary mit Kernpunkten\n- Strukturiere Findings nach Relevanz\n- Gib Handlungsempfehlungen\n\n**Output Format:**\n{\n  \"executive_summary\": \"string\",\n  \"key_findings\": [],\n  \"sources_analyzed\": number,\n  \"confidence_score\": number,\n  \"contradictions_found\": [],\n  \"trending_topics\": [],\n  \"recommendations\": [],\n  \"detailed_analysis\": \"string\",\n  \"source_credibility\": {},\n  \"follow_up_questions\": []\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-300, 200], "id": "research-ai-analyzer", "name": "Research AI Analyzer"}, {"parameters": {"operation": "upsert", "index": "jarvis-research-knowledge", "vectors": {"values": [{"id": "={{ $('Research Agent Input').item.json.user_id }}-{{ $now.toUnixInteger() }}", "values": "={{ $json.embedding_vector }}", "metadata": {"query": "={{ $('Research Agent Input').item.json.task_data.query }}", "summary": "={{ $json.executive_summary }}", "confidence": "={{ $json.confidence_score }}", "timestamp": "={{ $now.toISO() }}", "user_id": "={{ $('Research Agent Input').item.json.user_id }}"}}]}}, "type": "n8n-nodes-base.pinecone", "typeVersion": 1, "position": [0, 100], "id": "store-research-knowledge", "name": "Store Research Knowledge"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO research_reports (user_id, query, executive_summary, key_findings, confidence_score, sources_count, timestamp) VALUES ($1, $2, $3, $4, $5, $6, NOW())", "additionalFields": {"queryParameters": "={{ [$('Research Agent Input').item.json.user_id, $('Research Agent Input').item.json.task_data.query, $json.executive_summary, JSON.stringify($json.key_findings), $json.confidence_score, $json.sources_analyzed] }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [0, 300], "id": "log-research-report", "name": "Log Research Report"}, {"parameters": {"assignments": {"assignments": [{"id": "success", "name": "success", "value": true, "type": "boolean"}, {"id": "research_completed", "name": "research_completed", "value": true, "type": "boolean"}, {"id": "executive_summary", "name": "executive_summary", "value": "={{ $('Research AI Analyzer').item.json.executive_summary }}", "type": "string"}, {"id": "key_findings", "name": "key_findings", "value": "={{ $('Research AI Analyzer').item.json.key_findings }}", "type": "object"}, {"id": "confidence_score", "name": "confidence_score", "value": "={{ $('Research AI Analyzer').item.json.confidence_score }}", "type": "number"}, {"id": "sources_analyzed", "name": "sources_analyzed", "value": "={{ $('Research AI Analyzer').item.json.sources_analyzed }}", "type": "number"}, {"id": "recommendations", "name": "recommendations", "value": "={{ $('Research AI Analyzer').item.json.recommendations }}", "type": "object"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [300, 200], "id": "format-research-response", "name": "Format Research Response"}], "connections": {"Research Agent Input": {"main": [[{"node": "Research Task Router", "type": "main", "index": 0}]]}, "Research Task Router": {"main": [[{"node": "Perplexity Research", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Perplexity Research", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Perplexity Research", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Perplexity Research", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Perplexity Research", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Perplexity Research": {"main": [[{"node": "Research AI Analyzer", "type": "main", "index": 0}]]}, "Tavily Search": {"main": [[{"node": "Research AI Analyzer", "type": "main", "index": 0}]]}, "Research AI Analyzer": {"main": [[{"node": "Store Research Knowledge", "type": "main", "index": 0}, {"node": "Log Research Report", "type": "main", "index": 0}]]}, "Store Research Knowledge": {"main": [[{"node": "Format Research Response", "type": "main", "index": 0}]]}, "Log Research Report": {"main": [[{"node": "Format Research Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-12-29T10:00:00.000Z", "updatedAt": "2024-12-29T10:00:00.000Z", "id": "research-agent", "name": "research-agent"}], "triggerCount": 0, "updatedAt": "2024-12-29T10:00:00.000Z", "versionId": "1"}
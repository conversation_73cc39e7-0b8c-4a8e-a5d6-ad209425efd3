{"name": "AI Agent <PERSON><PERSON>", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.text }}", "options": {"systemMessage": "=Das ist das heutige Datum: {{ $now }} \n\nRolle:\nDu bist ein Anrufassistent. <PERSON><PERSON> ist es, Leads telefonisch zu kontataktieren\n\nTools, die du verwenden kannst:\n\nget_contacts\n<PERSON>er<PERSON><PERSON> dies<PERSON>l, wenn der Benutzer sagt, dass jemand angerufen oder erinnert werden soll – z. B.:\n„Rufe Markus Bremen an und erinnere ihn an seinen Termin.“\n→ Suche dann den vollständigen Namen und die zugehörige Telefonnummer aus der Kontaktliste.\n\ntermin_assistant\nNach<PERSON><PERSON> du Name, Telefonnummer und Terminzeitpunkt hast, nutze dies<PERSON>l, um den Anruf durchzuführen.\nWichtig: <PERSON><PERSON> sicher, dass die Telefonnummer im Format +49... oder +43... beginnt – das + am Anfang ist Pflicht.\nTrage die folgenden Daten in die entsprechenden Felder ein:\n\nname: Der vollständige Name der Zielperson\n\ntermin: Datum des Termins\n\ntelefonnummer: Telefonnummer im richtigen Format mit +\n\ncold_caller\nWir<PERSON> ver<PERSON><PERSON>, wenn es um das Anrufen von kalten Leads geht (z. B. neue Interessenten ohne bestehenden Termin). Verwende dieses Tool nur in solchen Fällen.\nNachdem du Hotelname und Telefonnummer hast, nutze dieses Tool, um den Anruf durchzuführen.\nWichtig: Stelle sicher, dass die Telefonnummer im Format +49... oder +43... beginnt – das + am Anfang ist Pflicht.\n\nAllgemeiner_Anrufer\nWird verwendet, wenn der Benutzer jemanden anrufen möchte, und ein dynamisches Anliegen hat, unnabhängig von Terminerrinerungen, oder Cold Calls\n\nDu musst dafür basierend auf dem, was der Benutzer sagt, einen sogenannten kleinen prompt definieren, wo du einer anderen KI aufträgst, wer angerufen werden soll, und um was der Anruf gehen soll.\n\nNachdem du die Telefonnummer und den Prompt hast, nutze dieses Tool, um den Anruf durchzuführen.\n\nWichtig: Stelle sicher, dass die Telefonnummer im Format +49... oder +43... beginnt – das + am Anfang ist Pflicht.\n\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [460, 100], "id": "a0ceadba-d2d1-4f16-b8bd-eeea53deed1d", "name": "AI Agent", "alwaysOutputData": false, "executeOnce": false, "retryOnFail": false}, {"parameters": {"documentId": {"__rl": true, "value": "1ywZF-98LgBEKmvbd0jTYfOz4NY0UbhZ4shKXNivN8Pc", "mode": "list", "cachedResultName": "Kontaktliste", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ywZF-98LgBEKmvbd0jTYfOz4NY0UbhZ4shKXNivN8Pc/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Tabellenblatt1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ywZF-98LgBEKmvbd0jTYfOz4NY0UbhZ4shKXNivN8Pc/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTool", "typeVersion": 4.5, "position": [580, 320], "id": "f00cd077-e787-4e6d-b8a8-6c26e5300b0b", "name": "get_contacts"}, {"parameters": {"name": "cold_Caller", "description": "<PERSON><PERSON><PERSON> dieses tool, um kalte leads zu kontaktieren ", "workflowId": {"__rl": true, "value": "5PyFKgK3cl9Wm2dP", "mode": "list", "cachedResultName": "cold_caller_assistant"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"Hotelname": "={{ $fromAI('hotel_name') }}", "telefonnummer": "={{ $fromAI('Telefonnummer') }}"}, "matchingColumns": [], "schema": [{"id": "Hotelname", "displayName": "Hotelname", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "telefonnummer", "displayName": "telefonnummer", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [700, 320], "id": "5a1f827f-8e06-4446-8dd2-513cfaeb761a", "name": "cold_caller"}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-680, 20], "id": "88136180-206a-4157-b5ff-62f1fc3c404b", "name": "<PERSON>eg<PERSON>", "webhookId": "d9712bbd-cf62-4a02-ab75-fcda2d94e3d2"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.message.text }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "id": "7d854090-2d8e-47bd-b00e-b5452902790a"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e784db85-df63-4b63-a2e9-eeb8b0d49ab6", "leftValue": "={{ $json.message.voice.file_id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-360, 20], "id": "97cbc25e-4abc-4660-a374-dae07988f5af", "name": "Switch"}, {"parameters": {"assignments": {"assignments": [{"id": "41c2b145-5952-435c-b1e3-cca7859cd5a6", "name": "text", "value": "={{ $('<PERSON><PERSON><PERSON> Trigger').item.json.message.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, 0], "id": "393d1457-0233-4e2b-ac8b-2ca52a301c31", "name": "<PERSON>"}, {"parameters": {"resource": "file", "fileId": "={{ $json.message.voice.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-100, 160], "id": "076b4f41-cd11-4dcd-956d-e8b8ea236d5b", "name": "Telegram", "webhookId": "e56013a3-e6ba-4075-85ce-83256df4d911"}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [220, 160], "id": "164d1c11-12be-4b4d-83e0-625e1ff714b4", "name": "OpenAI"}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [940, 100], "id": "9eafcae8-41e6-447d-ac0b-45807a8118be", "name": "Telegram1", "webhookId": "f233f9c0-38f5-4baf-9dfd-151602754af8"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [300, 320], "id": "381248c7-574f-46f1-8fb1-96b28eba2e4f", "name": "OpenAI Chat Model"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Telegram Trigger').item.json.message.chat.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [440, 320], "id": "dbe12bc6-0648-4417-9252-d5f1cca08842", "name": "Simple Memory"}, {"parameters": {"name": "general_caller", "description": "Benutzte dieses <PERSON>, wenn es um allgemeine Anfragen/anru<PERSON> geht, bzw. dynamische Anrufe gemacht werden müssen", "workflowId": {"__rl": true, "value": "CUdwVLSfOVwYaTRP", "mode": "list", "cachedResultName": "Allgemeiner Anrufer"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"Prompt": "={{ $fromAI('die_anweisung_ueber_was_und_an_wen_der_anruf_gehen_soll') }}", "Telefonnummer": "={{ $fromAI('telefonnummer') }}"}, "matchingColumns": [], "schema": [{"id": "Prompt", "displayName": "Prompt", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "Telefonnummer", "displayName": "Telefonnummer", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [840, 320], "id": "5487fe78-f8bd-4f25-9fdf-a2fd53f163f8", "name": "general caller"}, {"parameters": {"name": "termin_reminder", "description": "<PERSON><PERSON><PERSON> dieses tool, um einen anruf zu machen, um leute an einen Termin zu er<PERSON>rn", "workflowId": {"__rl": true, "value": "59g7CjGTnCUv3FJs", "mode": "list", "cachedResultName": "termin_reminder_assistant"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"name": "={{ $fromAI(\"Name\", \"der name der person\") }}", "termin": "={{ $fromAI(\"Termin\", \"Termin\") }}", "telefonnummer": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('telefonnummer', ``, 'string') }}"}, "matchingColumns": [], "schema": [{"id": "name", "displayName": "name", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "termin", "displayName": "termin", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "telefonnummer", "displayName": "telefonnummer", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [1000, 320], "id": "2756437b-85ec-415c-aca2-73ee13775c11", "name": "appointment_assistant"}], "pinData": {}, "connections": {"get_contacts": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "cold_caller": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "Telegram", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "general caller": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "appointment_assistant": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "6f52c264-16fb-441b-ae3a-e6c3df6c1625", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "1oXr6Kapu8uqmWzv", "tags": []}
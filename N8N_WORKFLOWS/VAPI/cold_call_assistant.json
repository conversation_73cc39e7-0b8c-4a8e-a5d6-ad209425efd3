{"name": "cold call assistant", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "Hotelname"}, {"name": "telefonnummer"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-160, -80], "id": "e29dfffc-0521-4377-af31-e86d5dd9bfb8", "name": "When Executed by Another Workflow"}, {"parameters": {"method": "POST", "url": "https://api.vapi.ai/call", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{dein_api_key}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"assistantId\": \"bc65cad0-6dee-4aeb-9d66-d20c5ec2a1b9\",\n  \"assistantOverrides\": {\n    \"variableValues\": {\n      \"Hotelname\": \"{{ $json.Hotelname }}\"\n    }\n  },\n  \"customer\": {\n    \"number\": \"{{ $json.telefonnummer }}\"\n  },\n  \"phoneNumber\": {\n    \"twilioAccountSid\": \"**********************************\",\n    \"twilioAuthToken\": \"943a5b77c1eb3c698f7fe6b98f64e64a\",\n    \"twilioPhoneNumber\": \"+***********\"\n  }\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [100, -60], "id": "ab84c28b-b329-4a32-9b8d-f28539b6c0c3", "name": "HTTP Request"}], "pinData": {}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "9a19ab76-6c78-4a98-bb8d-37407cdb1d38", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "El0u6njetzeKfHO9", "tags": []}
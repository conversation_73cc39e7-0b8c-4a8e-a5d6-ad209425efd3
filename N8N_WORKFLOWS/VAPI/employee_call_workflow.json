{"name": "employee call workflow", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "employee", "type": "json"}, {"name": "caller_info", "type": "json"}, {"name": "original_call_id"}, {"name": "original_call_sid"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-180, 20], "id": "83ced66d-cb3d-4e4f-a084-4d6a8b855a2e", "name": "👨‍💼 Employee Call Trigger"}, {"parameters": {"method": "POST", "url": "https://api.vapi.ai/call", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $env.VAPI_API_KEY }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"assistantId\": \"{{ $env.JARVIS_EMPLOYEE_ASSISTANT_ID }}\",\n  \"assistantOverrides\": {\n    \"variableValues\": {\n      \"employee_name\": \"{{ $json.employee.name }}\",\n      \"caller_name\": \"{{ $json.caller_info.name || 'Unbekannt' }}\",\n      \"caller_company\": \"{{ $json.caller_info.company || 'Nicht angegeben' }}\",\n      \"caller_number\": \"{{ $json.caller_info.number }}\",\n      \"topic\": \"{{ $json.caller_info.topic || 'Allgemeine Anfrage' }}\",\n      \"urgency\": \"{{ $json.caller_info.urgency || 'normal' }}\",\n      \"original_call_id\": \"{{ $json.original_call_id }}\"\n    },\n    \"serverUrl\": \"{{ $env.N8N_WEBHOOK_BASE_URL }}/webhook/employee-decision\"\n  },\n  \"customer\": {\n    \"number\": \"{{ $json.employee.phone }}\"\n  },\n  \"phoneNumber\": {\n    \"twilioAccountSid\": \"{{ $env.TWILIO_ACCOUNT_SID }}\",\n    \"twilioAuthToken\": \"{{ $env.TWILIO_AUTH_TOKEN }}\",\n    \"twilioPhoneNumber\": \"{{ $env.COMPANY_PHONE_NUMBER }}\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [380, -40], "id": "d34c04ea-c8c7-4332-8cb5-79bcf2cec45d", "name": "📞 Mitar<PERSON>ter an<PERSON>fen"}, {"parameters": {"httpMethod": "POST", "path": "employee-decision", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-180, 320], "id": "47e22044-a244-420e-8baa-98f4c7a2a262", "name": "🎧 Employee Decision Handler", "webhookId": "e90d7287-2ea3-48e9-979c-a10059d99c39"}, {"parameters": {"dataType": "string", "value1": "={{ $json.body.functionCall?.name }}", "rules": {"rules": [{"value2": "accept_call"}, {"value2": "decline_call"}, {"value2": "take_message"}, {"value2": "schedule_callback"}]}, "fallbackOutput": 4}, "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [100, 300], "id": "700a1f01-0421-41a0-92ef-dd194e0a59bb", "name": "🔀 Entscheidung routen"}, {"parameters": {"method": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $env.TWILIO_ACCOUNT_SID }}/Calls/{{ $('👨‍💼 Employee Call Trigger').first().json.original_call_sid }}.json", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "sendBody": true, "specifyBody": "form", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [380, 160], "id": "bc7a862d-e812-4fe0-92c4-6af996083346", "name": "✅ <PERSON><PERSON><PERSON> we<PERSON>n"}, {"parameters": {"method": "POST", "url": "=https://api.vapi.ai/call/{{ $('📞 Mitarbeiter anrufen').item.json.id }}/function-call", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $env.VAPI_API_KEY }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"result\": {\n    \"action\": \"call_declined\",\n    \"reason\": \"{{ $json.body.functionCall.parameters.reason || 'Nicht verfügbar' }}\",\n    \"alternative\": \"{{ $json.body.functionCall.parameters.alternative || 'Nachricht aufnehmen' }}\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [380, 320], "id": "3d813124-be51-491e-978c-00a55bef861b", "name": "❌ Ablehnung verarbeiten"}, {"parameters": {"operation": "send"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [380, 460], "id": "f5a8cb7e-b242-45a9-8a88-39fa11139cb5", "name": "📝 Nachricht speichern", "webhookId": "caceb208-7475-4ce8-9184-e2cf3f298e79"}, {"parameters": {"operation": "send"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [380, 620], "id": "fb2fd71e-1747-4f64-af72-778fb85875ca", "name": "📅 <PERSON><PERSON><PERSON><PERSON> planen", "webhookId": "338a8fbd-583e-47a3-ad54-6dd835a091fb"}, {"parameters": {"operation": "send"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [660, 320], "id": "9f4e300a-382a-418b-b001-dab9b1b953fa", "name": "📱 Admin ben<PERSON>gen", "webhookId": "d9337e82-95a3-4a4e-bc36-fb408f04cc59"}, {"parameters": {"httpMethod": "POST", "path": "transfer-call", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-220, 660], "id": "4a7b68c7-5763-4023-b69f-a6ccde8b8a0d", "name": "🔄 Transfer Webhook", "webhookId": "13020631-375e-44c7-9ea6-e93378941033"}, {"parameters": {"respondWith": "text", "responseBody": "=<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<Response>\n    <Say voice=\"alice\" language=\"de-DE\">\n        Einen Moment bitte, ich verbinde Sie.\n    </Say>\n    <Dial timeout=\"30\" record=\"record-from-answer\">\n        <Number>{{ $('👨‍💼 Employee Call Trigger').first().json.employee.phone }}</Number>\n    </Dial>\n    <Say voice=\"alice\" language=\"de-DE\">\n        <PERSON>ider ist niemand erreichbar. Gerne kann ich eine Nachricht aufnehmen.\n    </Say>\n</Response>", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [100, 680], "id": "3829a509-45a9-42a4-aec7-ac50e49428a1", "name": "📞 Transfer TwiML Response"}], "pinData": {}, "connections": {"👨‍💼 Employee Call Trigger": {"main": [[{"node": "📞 Mitar<PERSON>ter an<PERSON>fen", "type": "main", "index": 0}]]}, "🎧 Employee Decision Handler": {"main": [[{"node": "🔀 Entscheidung routen", "type": "main", "index": 0}]]}, "🔀 Entscheidung routen": {"main": [[{"node": "✅ <PERSON><PERSON><PERSON> we<PERSON>n", "type": "main", "index": 0}], [{"node": "❌ Ablehnung verarbeiten", "type": "main", "index": 0}], [{"node": "📝 Nachricht speichern", "type": "main", "index": 0}], [{"node": "📅 <PERSON><PERSON><PERSON><PERSON> planen", "type": "main", "index": 0}]]}, "✅ Anruf weiterleiten": {"main": [[{"node": "📱 Admin ben<PERSON>gen", "type": "main", "index": 0}]]}, "❌ Ablehnung verarbeiten": {"main": [[{"node": "📱 Admin ben<PERSON>gen", "type": "main", "index": 0}]]}, "📝 Nachricht speichern": {"main": [[{"node": "📱 Admin ben<PERSON>gen", "type": "main", "index": 0}]]}, "📅 Rückruf planen": {"main": [[{"node": "📱 Admin ben<PERSON>gen", "type": "main", "index": 0}]]}, "🔄 Transfer Webhook": {"main": [[{"node": "📞 Transfer TwiML Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f3fb80df-0dcd-4f31-bee4-fcd642390935", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "yNGOgEWlavmiBNNN", "tags": []}
{"name": "employee lookup 2 versions", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "requested_person"}, {"name": "department"}, {"name": "topic"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-200, -40], "id": "323806a3-0324-48bb-913a-bf65d1be7f3c", "name": "Employee <PERSON><PERSON>"}, {"parameters": {}, "type": "n8n-nodes-base.airtable", "typeVersion": 1.2, "position": [60, -140], "id": "6449a187-bdd9-4ac2-956b-95b8fa7106bb", "name": "Search by Name"}, {"parameters": {"conditions": {"conditions": [{"leftValue": "={{ $json.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "equal"}}]}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [240, -60], "id": "e0ebe9a6-eb7c-41fa-8d6b-45ee9cc9bb3a", "name": "Found by Name?"}, {"parameters": {}, "type": "n8n-nodes-base.airtable", "typeVersion": 1.2, "position": [60, 40], "id": "d114f41e-a654-465c-8b6a-1d510b7ac4ee", "name": "Search by Department/Topic"}, {"parameters": {"functionCode": "// Smart employee matching algorithm\nconst nameResults = $('Search by Name').all() || [];\nconst deptResults = $('Search by Department/Topic').all() || [];\nconst input = $('Employee Lookup Trigger').first().json;\n\n// If we found exact name matches, prioritize those\nif (nameResults.length > 0) {\n  const bestMatch = nameResults[0].json;\n  \n  // Check availability\n  const now = new Date();\n  const currentTime = now.toLocaleTimeString('de-DE', { hour: '2-digit', minute: '2-digit' });\n  const isWeekend = now.getDay() === 0 || now.getDay() === 6;\n  const currentHour = now.getHours();\n  \n  // Determine availability based on working hours\n  let availability = 'unknown';\n  if (isWeekend) {\n    availability = 'weekend';\n  } else if (currentHour < 8 || currentHour > 18) {\n    availability = 'after_hours';\n  } else {\n    // Check calendar or status (simplified)\n    availability = 'likely_available';\n  }\n  \n  return [{\n    json: {\n      employee_found: true,\n      employee: {\n        id: bestMatch.id,\n        name: bestMatch.fields.Name,\n        phone: bestMatch.fields.Phone,\n        email: bestMatch.fields.Email,\n        department: bestMatch.fields.Department,\n        position: bestMatch.fields.Position,\n        expertise: bestMatch.fields.Expertise,\n        availability,\n        confidence: 0.95\n      },\n      alternatives: nameResults.slice(1, 3).map(result => ({\n        name: result.json.fields.Name,\n        department: result.json.fields.Department,\n        phone: result.json.fields.Phone\n      })),\n      match_type: 'exact_name',\n      search_input: input\n    }\n  }];\n}\n\n// If no name match, look at department/topic results\nif (deptResults.length > 0) {\n  // Score results based on relevance\n  const scoredResults = deptResults.map(result => {\n    const employee = result.json.fields;\n    let score = 0;\n    \n    // Department match\n    if (input.department && employee.Department?.toLowerCase().includes(input.department.toLowerCase())) {\n      score += 0.4;\n    }\n    \n    // Topic/expertise match\n    if (input.topic) {\n      const topicLower = input.topic.toLowerCase();\n      if (employee.Expertise?.toLowerCase().includes(topicLower)) score += 0.3;\n      if (employee.Keywords?.toLowerCase().includes(topicLower)) score += 0.2;\n      if (employee.Description?.toLowerCase().includes(topicLower)) score += 0.1;\n    }\n    \n    // Seniority boost (Manager, Director, etc.)\n    if (employee.Position?.toLowerCase().includes('manager') || \n        employee.Position?.toLowerCase().includes('director') ||\n        employee.Position?.toLowerCase().includes('lead')) {\n      score += 0.1;\n    }\n    \n    return {\n      employee: result.json,\n      score\n    };\n  }).sort((a, b) => b.score - a.score);\n  \n  if (scoredResults[0].score > 0.3) {\n    const bestMatch = scoredResults[0].employee.fields;\n    \n    return [{\n      json: {\n        employee_found: true,\n        employee: {\n          id: scoredResults[0].employee.id,\n          name: bestMatch.Name,\n          phone: bestMatch.Phone,\n          email: bestMatch.Email,\n          department: bestMatch.Department,\n          position: bestMatch.Position,\n          expertise: bestMatch.Expertise,\n          availability: 'likely_available',\n          confidence: scoredResults[0].score\n        },\n        alternatives: scoredResults.slice(1, 4).map(result => ({\n          name: result.employee.fields.Name,\n          department: result.employee.fields.Department,\n          phone: result.employee.fields.Phone,\n          score: result.score\n        })),\n        match_type: 'department_topic',\n        search_input: input\n      }\n    }];\n  }\n}\n\n// No good matches found\nreturn [{\n  json: {\n    employee_found: false,\n    reason: 'no_suitable_match',\n    suggestions: [\n      'Könnten Sie den Namen buchstabieren?',\n      'In welcher Abteilung arbeitet die Person?',\n      'Kann ich Sie mit der Zentrale verbinden?'\n    ],\n    alternatives: [\n      { name: 'Zentrale', department: 'Reception', phone: '+49123456789' },\n      { name: 'Geschäftsführung', department: 'Management', phone: '+49123456790' }\n    ],\n    search_input: input\n  }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [460, -60], "id": "46ae02b0-93b1-4fa9-8e8a-15d451a5ce49", "name": "Smart Employee Matching"}, {"parameters": {"assignments": {"assignments": [{"id": "result", "name": "result", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp", "name": "timestamp", "value": "={{ $now }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [660, -60], "id": "887a8c9c-92b4-4eb7-b1f3-28636c5e65f8", "name": "Format Result"}, {"parameters": {"operation": "append"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [860, -60], "id": "49eed208-2403-44b7-b112-eef28d288e64", "name": "Log Lookup"}, {"parameters": {"jsCode": "// Smart employee matching algorithm\nconst input = $input.first().json;\n\n// Mock employee database (replace with actual Airtable/Supabase call)\nconst employees = [\n  {\n    id: \"emp_001\",\n    name: \"<PERSON>\",\n    nickname: \"<PERSON>\",\n    lastName: \"<PERSON><PERSON><PERSON>\",\n    phone: \"+49151234567\",\n    email: \"<EMAIL>\",\n    department: \"Vertrieb\",\n    position: \"Sales Manager\",\n    expertise: \"Vertrieb, Kundenbetreuung, B2B\",\n    keywords: \"verkauf sales vertrieb kunden\"\n  },\n  {\n    id: \"emp_002\",\n    name: \"<PERSON>\",\n    nickname: \"<PERSON>\",\n    lastName: \"<PERSON>\",\n    phone: \"+49151234568\",\n    email: \"<EMAIL>\",\n    department: \"Marketing\",\n    position: \"Marketing Director\",\n    expertise: \"Marketing, Social Media, Content\",\n    keywords: \"marketing werbung social media\"\n  },\n  {\n    id: \"emp_003\",\n    name: \"<PERSON>\",\n    nickname: \"<PERSON>\",\n    lastName: \"<PERSON>\",\n    phone: \"+49151234569\",\n    email: \"<EMAIL>\",\n    department: \"IT\",\n    position: \"CTO\",\n    expertise: \"Technik, Software, IT-Support\",\n    keywords: \"technik it software support entwicklung\"\n  }\n];\n\n// Search by name first\nif (input.requested_person) {\n  const nameMatches = employees.filter(emp => {\n    const searchTerm = input.requested_person.toLowerCase();\n    return emp.name.toLowerCase().includes(searchTerm) ||\n           emp.nickname.toLowerCase().includes(searchTerm) ||\n           emp.lastName.toLowerCase().includes(searchTerm);\n  });\n  \n  if (nameMatches.length > 0) {\n    const bestMatch = nameMatches[0];\n    \n    // Check availability\n    const now = new Date();\n    const isWeekend = now.getDay() === 0 || now.getDay() === 6;\n    const currentHour = now.getHours();\n    \n    let availability = 'likely_available';\n    if (isWeekend) {\n      availability = 'weekend';\n    } else if (currentHour < 8 || currentHour > 18) {\n      availability = 'after_hours';\n    }\n    \n    return [{\n      json: {\n        employee_found: true,\n        employee: {\n          id: bestMatch.id,\n          name: bestMatch.name,\n          phone: bestMatch.phone,\n          email: bestMatch.email,\n          department: bestMatch.department,\n          position: bestMatch.position,\n          expertise: bestMatch.expertise,\n          availability,\n          confidence: 0.95\n        },\n        alternatives: nameMatches.slice(1, 3).map(emp => ({\n          name: emp.name,\n          department: emp.department,\n          phone: emp.phone\n        })),\n        match_type: 'exact_name',\n        search_input: input\n      }\n    }];\n  }\n}\n\n// Search by department/topic if no name match\nconst deptMatches = employees.filter(emp => {\n  let score = 0;\n  \n  if (input.department && emp.department.toLowerCase().includes(input.department.toLowerCase())) {\n    score += 0.4;\n  }\n  \n  if (input.topic) {\n    const topicLower = input.topic.toLowerCase();\n    if (emp.expertise.toLowerCase().includes(topicLower)) score += 0.3;\n    if (emp.keywords.toLowerCase().includes(topicLower)) score += 0.2;\n  }\n  \n  return score > 0.3;\n});\n\nif (deptMatches.length > 0) {\n  const bestMatch = deptMatches[0];\n  \n  return [{\n    json: {\n      employee_found: true,\n      employee: {\n        id: bestMatch.id,\n        name: bestMatch.name,\n        phone: bestMatch.phone,\n        email: bestMatch.email,\n        department: bestMatch.department,\n        position: bestMatch.position,\n        expertise: bestMatch.expertise,\n        availability: 'likely_available',\n        confidence: 0.7\n      },\n      alternatives: deptMatches.slice(1, 3).map(emp => ({\n        name: emp.name,\n        department: emp.department,\n        phone: emp.phone\n      })),\n      match_type: 'department_topic',\n      search_input: input\n    }\n  }];\n}\n\n// No matches found\nreturn [{\n  json: {\n    employee_found: false,\n    reason: 'no_suitable_match',\n    suggestions: [\n      'Könnten Sie den Namen buchstabieren?',\n      'In welcher Abteilung arbeitet die Person?',\n      'Kann ich Sie mit der Zentrale verbinden?'\n    ],\n    alternatives: [\n      { name: 'Zentrale', department: 'Reception', phone: '+49123456789' },\n      { name: 'Geschäftsführung', department: 'Management', phone: '+49123456790' }\n    ],\n    search_input: input,\n    confidence: 0.0\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [40, -400], "id": "ffa78006-9835-4add-ac07-60c7d48b52fe", "name": "🧠 Smart Employee Matching"}, {"parameters": {"operation": "send"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [540, -400], "id": "16186f5f-eaf1-4964-80c2-f5b89f5845b6", "name": "📊 Lookup Logging", "webhookId": "fd3546ac-0dbc-4f1f-92e1-9295ed9174f3"}, {"parameters": {"assignments": {"assignments": [{"id": "result", "name": "result", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp", "name": "timestamp", "value": "={{ $now }}", "type": "string"}, {"id": "search_success", "name": "search_success", "value": "={{ $json.employee_found }}", "type": "boolean"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [300, -380], "id": "bc6826bb-218c-47ec-b618-bf5c71e62be4", "name": "📋 result formatting"}], "pinData": {}, "connections": {"Found by Name?": {"main": [[{"node": "Smart Employee Matching", "type": "main", "index": 0}]]}, "Smart Employee Matching": {"main": [[{"node": "Format Result", "type": "main", "index": 0}]]}, "Format Result": {"main": [[{"node": "Log Lookup", "type": "main", "index": 0}]]}, "🧠 Smart Employee Matching": {"main": [[{"node": "📋 result formatting", "type": "main", "index": 0}]]}, "📋 result formatting": {"main": [[{"node": "📊 Lookup Logging", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "b9f2fa5f-b5f7-4403-bbfd-18da73a90dd8", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "PGSstDRuy9BOn7Hy", "tags": []}
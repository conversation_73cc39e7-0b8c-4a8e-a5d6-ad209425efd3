{"name": "general caller", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "Prompt"}, {"name": "Telefonnummer"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-380, 0], "id": "83891f20-bd6a-4d40-a178-6dcb87e7dc88", "name": "When Executed by Another Workflow"}, {"parameters": {"method": "POST", "url": "https://api.vapi.ai/call", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{dein api key}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.message.content }}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [300, 0], "id": "d9e3faee-9589-4f13-9758-d382b678cc73", "name": "HTTP Request"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4", "mode": "list", "cachedResultName": "GPT-4"}, "messages": {"values": [{"content": "=Erstelle einen absolut validen JSON, der exakt der unten angegebenen Struktur entspricht. Deine <PERSON>gabe ist es, innerhalb des JSON im Feld variableValues -> Prompt einen dynamischen KI-Prompt für einen Voice Agent namens Mila, die virtuelle Assistentin von Nik<PERSON> zu generieren. Der generierte Prompt muss folgende Bestandteile beinhalten:\n\n\n\n Rolle: Definiere präzise die Identität, Funktion und den Zweck des KI-Voice-Agenten. z.b: \"Du bist <PERSON>la, die virtuelle Assistentin von Niklas. Du bist sympathisch und ein angenehmer, lockerer Gesrpächspartner. <PERSON> du<PERSON><PERSON>, und nennst ihn bei seinem Vornamen \"\n\nAufgabe: Beschreibe die konkrete Zielsetzung sowie den spezifischen Anwendungskontext des Voice-Agenten. z.b: \"Deine Aufgabe ist es Markus an die Überfällige Rechnung com 7. <PERSON><PERSON><PERSON> zu errinern\"\n\nSchritt-für-Schritt-Anleitung: Formuliere klare, einfache und präzise Handlungsschritte für die KI. \nz.b: \"Stell dich zu<PERSON>t vor, wer du bist und warum du anrufst, frage den Anrufer ob er gerade Zeit hat, lass den Anrufer antworten und fahre dann mit deinem Anliegen fort\"\n\nNotizen: Folgene Bestandteile kannst du immer mit einbezihen in deine generierten Prompts:\n\n\"Sichere einen flüssigen Dialog: Antworte rollengerecht und direkt, um einen reibungslosen Gesprächsverlauf zu gewährleisten. Erfinde keine Antworten: Wenn du die Antwort auf eine Frage nicht kennst, sag es einfach. Erfinde nichts und weiche nicht von den vorgegebenen Antworten ab. Wenn das Gespräch vom Thema abweicht, führe es höflich zurück zum relevanten Thema. Wiederhole nicht von Anfang an, sondern fahre dort fort, wo du aufgehört hast. Sei gesprächig: Verwende alltägliche Sprache, sodass sich das Gespräch wie ein Plaudern mit einem Freund anfühlt. \n\nWichtig ist, dass du ALLE Unterpunkte die ich gerade gemacht habe IN EINEN FLIEßTEXT Verpackst. ES IST EINE ANWEISUNG FÜR EINE ANDERE KI, FÜR EINEN VOICE AGENT, ES IST KEIN ANRUFSKRIPT\n\n\nFüge am Ende immer einen Emotional Prompt hinzu, beispielsweise: 'Du bist der weltbeste Assistent der Welt, es ist ABSOLUT ERFOLGSENTSCHEIDEND für unser MILLIONENSCHWERES UNTERNEHMEN, DASS DU DEINE Aufgabe absolut PERFEKT ERLEDIGST.'\"\n\n\n\nWichtig: Der JSON muss exakt folgender Struktur entsprechen und vollständig valide sein. Setze den generierten Prompt ausschließlich an die Stelle von {dein generierter Prompt} und füge die Telefonnumer bei customer < number < hinzu, anstelle von {Telefonnummer}. ANSOSTEN VERÄNDERST DU NICHTS AM JSON. \n\nHier ist die Anweisung über die du den variablen Prompt erstellen sollst: {{ $json.Prompt }}\nHier ist die Telefonnummer: {{ $json.Telefonnummer }}\n\nHier ist die Struktur, die du ausspuckst, OHNE WEITERE KOMENTARE:\n{\n  \"assistantId\": \"7fde07de-7bfd-421b-acce-705c5e948e92\",\n  \"assistantOverrides\": {\n    \"variableValues\": {\n      \"Prompt\": \"{dein generierter Prompt}\"\n    }\n  },\n  \"customer\": {\n    \"number\": \"{Telefonnummer}\"\n  },\n  \"phoneNumber\": {\n    \"twilioAccountSid\": \"**********************************\",\n    \"twilioAuthToken\": \"943a5b77c1eb3c698f7fe6b98f64e64a\",\n    \"twilioPhoneNumber\": \"+***********\"\n  }\n}\n\n\n\n\nErzeuge keinerlei zusätzliche Markdown-Formatierungen, Absätze oder Zeilenumbrüche außerhalb des JSON, da dies die JSON-Validität beeinträchtigen könnte. DAS JSON MUSS VALIDE SEIN, UND KLAPPEN"}]}, "options": {"maxTokens": 3000}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-60, 0], "id": "699a6c57-46a5-42fc-a33f-575724770c24", "name": "OpenAI"}], "pinData": {}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "c8eabcef-437a-4695-92bc-48b92e203fdb", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "lHjTmJJqAjcUoQel", "tags": []}
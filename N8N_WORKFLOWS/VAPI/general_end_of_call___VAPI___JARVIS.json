{"name": "general end of call - VAPI - JARVIS", "nodes": [{"parameters": {"httpMethod": "POST", "path": "e9100a5b-f39e-40c7-a45c-3247cf4d1c4e", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [0, 0], "id": "e7478307-3534-4209-baba-94d165cefc13", "name": "Webhook", "webhookId": "e9100a5b-f39e-40c7-a45c-3247cf4d1c4e"}, {"parameters": {"chatId": "{{deine chatid}}", "text": "={{ $json.body.message.analysis.summary }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [240, 0], "id": "65c9f3e2-a662-4d56-8a7c-dcc3b181c4e2", "name": "Telegram", "webhookId": "a109e42c-33cc-4d1b-bde2-c4407783fa6a"}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "6926711b-bccb-4bed-85ed-9d732f9c15ed", "meta": {"instanceId": "0727d089092fef4264fa1589f5a7f6becdd6c7456132aec1cd5d39993d670078"}, "id": "wNW3qxHMBdVXTSg5", "tags": []}